// src/router/index.js
import Vue from 'vue'
import VueRouter from 'vue-router'
 
Vue.use(VueRouter)
 
const routes = [
  { path: '/', name: 'Login', component: () => import('../pages/login/login') },
  { path: '/reg', name: 'Register', component: () => import('../pages/register/register') }
]
 
const router = new VueRouter({
  mode: 'history', // 使用HTML5 History模式
  base: process.env.BASE_URL,
  routes
})
 
export default router