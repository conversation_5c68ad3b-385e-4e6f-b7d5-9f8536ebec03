import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../pages/login/login.vue')
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../pages/login/register.vue')
  },
  {
    path: '/index',
    name: 'Index',
    component: () => import('../pages/index/index.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/table',
    name: 'Table',
    component: () => import('../pages/table/table.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/coach',
    name: 'Coach',
    component: () => import('../pages/coach/coach.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/appointment',
    name: 'Appointment',
    component: () => import('../pages/appointment/appointment.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/mine',
    name: 'Mine',
    component: () => import('../pages/mine/mine.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('merchant_token')
  
  if (to.meta.requiresAuth && !token) {
    // 需要认证但没有token，跳转到登录页
    next('/login')
  } else if (to.path === '/login' && token) {
    // 已登录用户访问登录页，跳转到首页
    next('/index')
  } else {
    next()
  }
})

export default router 