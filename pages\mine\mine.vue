<template>
	<view class="mine-container">
		<!-- 头部个人信息 -->
		<view class="header-card">
			<view class="merchant-info">
				<image class="merchant-avatar" :src="merchantInfo.logoUrl || '/static/images/default-logo.png'" mode="aspectFill"></image>
				<view class="merchant-detail">
					<text class="merchant-name">{{merchantInfo.merchantName}}</text>
					<view class="merchant-address">
						<uni-icons type="location" size="14" color="#999999"></uni-icons>
						<text class="address-text">{{merchantInfo.address}}</text>
					</view>
				</view>
				<view class="edit-btn" @click="navigateTo('/pages/mine/profile')">
					<uni-icons type="right" size="16" color="#999999"></uni-icons>
				</view>
			</view>
			<view class="merchant-stats">
				<view class="stat-item">
					<text class="stat-value">{{merchantInfo.orderCount || 0}}</text>
					<text class="stat-label">累计订单</text>
				</view>
				<view class="stat-item">
					<text class="stat-value">{{merchantInfo.daysJoined || 0}}</text>
					<text class="stat-label">入驻天数</text>
				</view>
				<view class="stat-item">
					<text class="stat-value">{{merchantInfo.rating || '0.0'}}</text>
					<text class="stat-label">店铺评分</text>
				</view>
			</view>
		</view>
		
		<!-- 营业收入卡片 -->
		<view class="income-card">
			<view class="income-header">
				<text class="income-title">营业收入（元）</text>
				<view class="date-picker" @click="showDatePicker = true">
					<text class="date-text">{{currentDateRange}}</text>
					<uni-icons type="calendar" size="16" color="#007AFF"></uni-icons>
				</view>
			</view>
			<view class="income-amount">{{incomeData.total.toFixed(2)}}</view>
			<view class="income-detail">
				<view class="detail-item">
					<text class="detail-label">台球桌收入</text>
					<text class="detail-value">{{incomeData.table.toFixed(2)}}</text>
				</view>
				<view class="detail-item">
					<text class="detail-label">教练课程收入</text>
					<text class="detail-value">{{incomeData.coach.toFixed(2)}}</text>
				</view>
				<view class="detail-item">
					<text class="detail-label">其他收入</text>
					<text class="detail-value">{{incomeData.other.toFixed(2)}}</text>
				</view>
			</view>
		</view>
		
		<!-- 功能菜单 -->
		<view class="menu-card">
			<view class="menu-group">
				<view class="menu-title">营业管理</view>
				<view class="menu-list">
					<view class="menu-item" @click="navigateTo('/pages/merchant/business-hours')">
						<view class="menu-icon business-icon">
							<uni-icons type="calendar" size="24" color="#FFFFFF"></uni-icons>
						</view>
						<text class="menu-name">营业时间</text>
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
					<view class="menu-item" @click="navigateTo('/pages/merchant/pricing')">
						<view class="menu-icon price-icon">
							<uni-icons type="rmb" size="24" color="#FFFFFF"></uni-icons>
						</view>
						<text class="menu-name">价格设置</text>
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
					<view class="menu-item" @click="navigateTo('/pages/coupon/coupon')">
						<view class="menu-icon coupon-icon">
							<uni-icons type="gift" size="24" color="#FFFFFF"></uni-icons>
						</view>
						<text class="menu-name">优惠券管理</text>
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
					<view class="menu-item" @click="navigateTo('/pages/merchant/promotion')">
						<view class="menu-icon promotion-icon">
							<uni-icons type="star" size="24" color="#FFFFFF"></uni-icons>
						</view>
						<text class="menu-name">优惠活动</text>
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
				</view>
			</view>
			
			<view class="menu-group">
				<view class="menu-title">数据统计</view>
				<view class="menu-list">
					<view class="menu-item" @click="navigateTo('/pages/merchant/statistics')">
						<view class="menu-icon stats-icon">
							<uni-icons type="chart" size="24" color="#FFFFFF"></uni-icons>
						</view>
						<text class="menu-name">营业统计</text>
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
					<view class="menu-item" @click="navigateTo('/pages/merchant/customer')">
						<view class="menu-icon customer-icon">
							<uni-icons type="contact" size="24" color="#FFFFFF"></uni-icons>
						</view>
						<text class="menu-name">顾客管理</text>
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
				</view>
			</view>
			
			<view class="menu-group">
				<view class="menu-title">系统设置</view>
				<view class="menu-list">
					<view class="menu-item" @click="navigateTo('/pages/mine/profile')">
						<view class="menu-icon profile-icon">
							<uni-icons type="shop" size="24" color="#FFFFFF"></uni-icons>
						</view>
						<text class="menu-name">商家信息</text>
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
					<view class="menu-item" @click="navigateTo('/pages/mine/setting')">
						<view class="menu-icon setting-icon">
							<uni-icons type="gear" size="24" color="#FFFFFF"></uni-icons>
						</view>
						<text class="menu-name">系统设置</text>
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
					<view class="menu-item" @click="callService">
						<view class="menu-icon service-icon">
							<uni-icons type="headphones" size="24" color="#FFFFFF"></uni-icons>
						</view>
						<text class="menu-name">联系客服</text>
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 退出登录 -->
		<view class="logout-container">
			<button class="logout-btn" @click="logout">退出登录</button>
		</view>
		
		<!-- 日期选择器弹窗 -->
		<uni-calendar 
			:insert="false"
			:start-date="startDate"
			:end-date="endDate"
			:range="true"
			v-model="showDatePicker" 
			@confirm="dateConfirm"
		/>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { getMerchantInfo } from '../../api/merchant/auth.js';
import { getRevenueStats } from '../../api/merchant/stats.js';

// 初始化日期
const today = new Date();
const year = today.getFullYear();
const month = today.getMonth();
const day = today.getDate();

// 默认显示本月数据
const monthStart = new Date(year, month, 1);
const monthEnd = new Date(year, month + 1, 0);

// 响应式数据
const merchantInfo = ref({
	merchantId: '',
	merchantName: '',
	logoUrl: '',
	contactPerson: '',
	contactPhone: '',
	address: '',
	description: '',
	orderCount: 0,
	daysJoined: 0,
	rating: '0.0'
});

const incomeData = ref({
	total: 0,
	table: 0,
	coach: 0,
	other: 0
});

const dateRange = ref([
	formatDate(monthStart),
	formatDate(monthEnd)
]);

const startDate = ref(''); // 日历开始日期
const endDate = ref(''); // 日历结束日期
const showDatePicker = ref(false);
const token = ref(''); // 商家token

// 计算属性
const currentDateRange = computed(() => {
	if (dateRange.value.length === 2) {
		return `${dateRange.value[0]} 至 ${dateRange.value[1]}`;
	}
	return '本月';
});

// 页面加载时
onMounted(() => {
	// 获取存储的token
	token.value = uni.getStorageSync('merchant_token');
	
	// 加载商家信息和收入数据
	loadMerchantInfo();
	loadIncomeData();
});

// 格式化日期
function formatDate(date) {
	const year = date.getFullYear();
	const month = String(date.getMonth() + 1).padStart(2, '0');
	const day = String(date.getDate()).padStart(2, '0');
	return `${year}-${month}-${day}`;
}

// 加载商家信息
const loadMerchantInfo = async () => {
	try {
		// 优先从本地存储获取基本信息
		const storedInfo = uni.getStorageSync('merchant_info');
		if (storedInfo) {
			const info = JSON.parse(storedInfo);
			merchantInfo.value = {
				...merchantInfo.value,
				...info
			};
		}
		
		// 调用API获取最新的商家信息
		const response = await getMerchantInfo();
		
		if (response.code === 200) {
			const data = response.data;
			merchantInfo.value = {
				merchantId: data.id || '',
				merchantName: data.name || '',
				logoUrl: data.logo_url || '/static/images/default-logo.png',
				contactPerson: data.contact_person || '',
				contactPhone: data.contact_phone || '',
				address: data.address || '',
				description: data.description || '',
				orderCount: data.total_orders || 0,
				daysJoined: data.days_joined || 0,
				rating: data.rating || '0.0'
			};
			
			// 更新本地存储
			uni.setStorageSync('merchant_info', JSON.stringify(merchantInfo.value));
		}
	} catch (error) {
		console.error('加载商家信息失败', error);
		// 如果API失败，保持使用本地存储的数据或默认数据
		if (!merchantInfo.value.merchantName) {
			// 使用默认数据
			merchantInfo.value = {
				merchantId: '1001',
				merchantName: '星牌台球俱乐部',
				logoUrl: '/static/images/default-logo.png',
				contactPerson: '张老板',
				contactPhone: '13800138000',
				address: '北京市朝阳区xxx路xxx号',
				description: '专业台球俱乐部，环境优雅，设备一流',
				orderCount: 1258,
				daysJoined: 365,
				rating: '4.8'
			};
		}
	}
};

// 加载收入数据
const loadIncomeData = async () => {
	try {
		uni.showLoading({
			title: '加载中...'
		});

		const response = await getRevenueStats({
			start_date: dateRange.value[0],
			end_date: dateRange.value[1]
		});

		uni.hideLoading();

		if (response.code === 200) {
			const data = response.data;
			incomeData.value = {
				total: data.total_revenue || 0,
				table: data.table_revenue || 0,
				coach: data.coach_revenue || 0,
				other: data.other_revenue || 0
			};
		} else {
			// API失败时使用默认数据
			incomeData.value = {
				total: 28560.50,
				table: 18560.00,
				coach: 8500.50,
				other: 1500.00
			};
			
			uni.showToast({
				title: response.msg || '获取收入数据失败',
				icon: 'none'
			});
		}
	} catch (error) {
		uni.hideLoading();
		console.error('加载收入数据失败', error);
		
		// 网络错误时使用默认数据
		incomeData.value = {
			total: 28560.50,
			table: 18560.00,
			coach: 8500.50,
			other: 1500.00
		};
		
		uni.showToast({
			title: '加载数据失败，显示离线数据',
			icon: 'none'
		});
	}
};

// 日期确认
const dateConfirm = (range) => {
	if (range && range.length === 2) {
		dateRange.value = range;
		loadIncomeData(); // 重新加载收入数据
	}
	showDatePicker.value = false;
};

// 导航到指定页面
const navigateTo = (url) => {
	uni.navigateTo({
		url: url
	});
};

// 联系客服
const callService = () => {
	uni.showActionSheet({
		itemList: ['拨打客服电话', '在线客服'],
		success: (res) => {
			if (res.tapIndex === 0) {
				uni.makePhoneCall({
					phoneNumber: '************'
				});
			} else {
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			}
		}
	});
};

// 退出登录
const logout = () => {
	uni.showModal({
		title: '退出登录',
		content: '确定要退出登录吗？',
		success: (res) => {
			if (res.confirm) {
				// 清除本地存储的登录信息
				uni.removeStorageSync('merchant_token');
				uni.removeStorageSync('merchant_info');
				uni.removeStorageSync('merchantUserInfo');
				
				// 跳转到登录页
				uni.reLaunch({
					url: '/pages/login/login'
				});
				
				uni.showToast({
					title: '已退出登录',
					icon: 'success'
				});
			}
		}
	});
};
</script>

<style lang="scss">
	.mine-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 30rpx;
	}
	
	.header-card {
		background-color: #007AFF;
		padding: 30rpx;
		color: #FFFFFF;
		position: relative;
	}
	
	.merchant-info {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.merchant-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		background-color: #FFFFFF;
		margin-right: 20rpx;
	}
	
	.merchant-detail {
		flex: 1;
	}
	
	.merchant-name {
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
	}
	
	.merchant-address {
		display: flex;
		align-items: center;
		opacity: 0.8;
	}
	
	.address-text {
		font-size: 24rpx;
		margin-left: 6rpx;
	}
	
	.edit-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
	}
	
	.merchant-stats {
		display: flex;
		background-color: rgba(255, 255, 255, 0.1);
		padding: 20rpx 0;
		border-radius: 10rpx;
	}
	
	.stat-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: relative;
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			right: 0;
			top: 15rpx;
			bottom: 15rpx;
			width: 1rpx;
			background-color: rgba(255, 255, 255, 0.2);
		}
	}
	
	.stat-value {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 6rpx;
	}
	
	.stat-label {
		font-size: 24rpx;
		opacity: 0.8;
	}
	
	.income-card {
		background-color: #FFFFFF;
		margin: 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.income-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.income-title {
		font-size: 28rpx;
		color: #666666;
	}
	
	.date-picker {
		display: flex;
		align-items: center;
		background-color: #F0F7FF;
		padding: 6rpx 16rpx;
		border-radius: 20rpx;
	}
	
	.date-text {
		font-size: 24rpx;
		color: #007AFF;
		margin-right: 10rpx;
	}
	
	.income-amount {
		font-size: 60rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
	}
	
	.income-detail {
		background-color: #F9F9F9;
		padding: 20rpx;
		border-radius: 10rpx;
	}
	
	.detail-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 10rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
	
	.detail-label {
		font-size: 26rpx;
		color: #666666;
	}
	
	.detail-value {
		font-size: 26rpx;
		color: #333333;
		font-weight: 500;
	}
	
	.menu-card {
		background-color: #FFFFFF;
		margin: 0 20rpx 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.menu-group {
		&:not(:last-child) {
			margin-bottom: 20rpx;
			border-bottom: 20rpx solid #F5F5F5;
		}
	}
	
	.menu-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #F5F5F5;
	}
	
	.menu-item {
		display: flex;
		align-items: center;
		padding: 30rpx;
		position: relative;
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			left: 30rpx;
			right: 30rpx;
			bottom: 0;
			height: 1rpx;
			background-color: #F5F5F5;
		}
	}
	
	.menu-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}
	
	.business-icon {
		background-color: #007AFF;
	}
	
	.price-icon {
		background-color: #00CC66;
	}
	
	.coupon-icon {
		background-color: #FF6B9D;
	}

	.promotion-icon {
		background-color: #FF9500;
	}
	
	.stats-icon {
		background-color: #5856D6;
	}
	
	.customer-icon {
		background-color: #FF3B30;
	}
	
	.profile-icon {
		background-color: #007AFF;
	}
	
	.setting-icon {
		background-color: #8E8E93;
	}
	
	.service-icon {
		background-color: #34C759;
	}
	
	.menu-name {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
	}
	
	.logout-container {
		margin-top: 20rpx;
		text-align: center;
	}
	
	.logout-btn {
		background-color: #FF3B30;
		color: #FFFFFF;
		padding: 20rpx 40rpx;
		border-radius: 40rpx;
		font-size: 28rpx;
	}
</style> 