<template>
	<view class="detail-container">
		<form @submit="submitForm">
			<!-- 头部信息 -->
			<view class="header-card">
				<view class="avatar-box">
					<image class="table-image" :src="tableInfo.image || '/static/images/default-table.png'" mode="aspectFill"></image>
					<view class="upload-btn" @click="chooseImage">
						<uni-icons type="camera" size="20" color="#FFFFFF"></uni-icons>
					</view>
				</view>
				<view class="base-info">
					<input class="table-name" type="text" v-model="tableInfo.name" placeholder="请输入台球桌名称" :disabled="viewMode"/>
					<view class="status-tag" :class="tableInfo.status === '1' ? 'status-available' : 'status-occupied'">
						{{tableInfo.status === '1' ? '空闲中' : '使用中'}}
					</view>
				</view>
			</view>
			
			<!-- 基本信息 -->
			<view class="info-card">
				<view class="card-title">基本信息</view>
				<view class="form-item">
					<text class="form-label">台球桌类型</text>
					<view class="form-content">
						<picker @change="typeChange" :value="typeIndex" :range="typeOptions" :disabled="viewMode">
							<view class="picker-view">
								{{typeOptions[typeIndex]}}
								<uni-icons type="bottom" size="14" color="#666666"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">台球桌尺寸</text>
					<view class="form-content">
						<picker @change="sizeChange" :value="sizeIndex" :range="sizeOptions" :disabled="viewMode">
							<view class="picker-view">
								{{sizeOptions[sizeIndex]}}
								<uni-icons type="bottom" size="14" color="#666666"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">台球桌区域</text>
					<view class="form-content">
						<picker @change="areaChange" :value="areaIndex" :range="areaOptions" :disabled="viewMode">
							<view class="picker-view">
								{{areaOptions[areaIndex]}}
								<uni-icons type="bottom" size="14" color="#666666"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">每小时收费</text>
					<view class="form-content">
						<input type="digit" v-model="tableInfo.pricePerHour" placeholder="请输入每小时收费(元)" :disabled="viewMode"/>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">状态</text>
					<view class="form-content">
						<picker @change="statusChange" :value="statusIndex" :range="statusOptions" :disabled="viewMode">
							<view class="picker-view">
								{{statusOptions[statusIndex]}}
								<uni-icons type="bottom" size="14" color="#666666"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
			</view>
			
			<!-- 特性标签 -->
			<view class="info-card">
				<view class="card-title">特性标签</view>
				<view class="tags-container">
					<view 
						class="tag-item" 
						v-for="(tag, index) in tableInfo.tags" 
						:key="index"
						:class="{active: selectedTags.includes(tag)}"
						@click="toggleTag(tag)"
						v-if="!viewMode"
					>
						{{tag}}
					</view>
					<view class="tag-item add-tag" @click="showAddTagDialog" v-if="!viewMode">
						<uni-icons type="plusempty" size="14" color="#007AFF"></uni-icons>
						<text>添加</text>
					</view>
					
					<view class="tag-item" v-for="(tag, index) in tableInfo.tags" :key="index" v-if="viewMode">
						{{tag}}
					</view>
					<view class="empty-tip" v-if="tableInfo.tags.length === 0 && viewMode">
						暂无特性标签
					</view>
				</view>
			</view>
			
			<!-- 台球桌描述 -->
			<view class="info-card">
				<view class="card-title">台球桌描述</view>
				<view class="form-item">
					<textarea 
						class="form-textarea" 
						v-model="tableInfo.description" 
						placeholder="请输入台球桌描述，如台球桌品牌、特性等" 
						:disabled="viewMode"
					/>
				</view>
			</view>
			
			<!-- 按钮区域 -->
			<view class="bottom-actions">
				<button class="action-btn cancel-btn" @click="goBack">返回</button>
				<button class="action-btn primary-btn" type="primary" form-type="submit" v-if="!viewMode">保存</button>
				<button class="action-btn edit-btn" @click="switchToEdit" v-if="viewMode && canEdit">编辑</button>
				<button class="action-btn qrcode-btn" @click="showQRCode" v-if="viewMode">查看二维码</button>
			</view>
		</form>
		
		<!-- 添加标签弹窗 -->
		<uni-popup ref="tagPopup" type="dialog">
			<uni-popup-dialog
				title="添加特性标签"
				:type="dialogType"
				:before-close="true"
				confirmText="确定"
				cancelText="取消"
				@confirm="addNewTag"
				@close="closeDialog"
			>
				<uni-easyinput
					v-model="newTag"
					placeholder="请输入标签名称（如：斯诺克专用、比赛台等）"
					:maxlength="10"
				/>
			</uni-popup-dialog>
		</uni-popup>
		
		<!-- 二维码弹窗 -->
		<uni-popup ref="qrcodePopup" type="center">
			<view class="qrcode-container">
				<view class="qrcode-title">台球桌二维码</view>
				<view class="qrcode-content">
					<image class="qrcode-image" src="/static/images/qrcode.png" mode="aspectFit"></image>
				</view>
				<view class="qrcode-tips">用户可通过扫描此二维码预约台球桌</view>
				<view class="qrcode-actions">
					<button class="qrcode-btn save-btn" @click="saveQRCode">保存二维码</button>
					<button class="qrcode-btn close-btn" @click="closeQRCode">关闭</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: 0,
				type: '', // 'add': 新增, 'edit': 编辑, 'view': 查看
				viewMode: true,
				canEdit: true,
				tableInfo: {
					id: 0,
					name: '',
					image: '',
					type: '1', // 1: 中式, 2: 美式, 3: 英式, 4: 斯诺克
					size: '1', // 1: 标准9尺, 2: 标准8尺, 3: 小型7尺
					area: '1', // 1: 普通区, 2: VIP区, 3: 包间
					status: '1', // 1: 空闲中, 0: 使用中
					pricePerHour: '',
					tags: [],
					description: ''
				},
				typeIndex: 0,
				typeOptions: ['中式台球桌', '美式台球桌', '英式台球桌', '斯诺克台球桌'],
				typeValues: ['1', '2', '3', '4'],
				sizeIndex: 0,
				sizeOptions: ['标准9尺', '标准8尺', '小型7尺'],
				sizeValues: ['1', '2', '3'],
				areaIndex: 0,
				areaOptions: ['普通区', 'VIP区', '包间'],
				areaValues: ['1', '2', '3'],
				statusIndex: 0,
				statusOptions: ['使用中', '空闲中'],
				statusValues: ['0', '1'],
				selectedTags: [],
				commonTags: ['斯诺克专用', '比赛台', '初学者推荐', '专业台', 'VIP专享'],
				newTag: '',
				dialogType: 'info'
			}
		},
		onLoad(options) {
			if (options.id) {
				this.id = options.id;
				this.type = options.type || 'view';
				this.loadTableDetail();
			} else if (options.type === 'add') {
				this.type = 'add';
				this.viewMode = false;
				this.canEdit = true;
				// 初始化默认值
				this.tableInfo = {
					id: 0,
					name: '',
					image: '',
					type: '1',
					size: '1',
					area: '1',
					status: '1',
					pricePerHour: '',
					tags: [],
					description: ''
				};
			}
			
			// 如果是编辑模式，设置viewMode为false
			if (this.type === 'edit') {
				this.viewMode = false;
			}
		},
		methods: {
			loadTableDetail() {
				// 从API获取台球桌详情
				// 这里使用模拟数据
				uni.showLoading({
					title: '加载中'
				});
				
				setTimeout(() => {
					// 模拟数据
					this.tableInfo = {
						id: 1,
						name: '1号台',
						image: '',
						type: '4',
						size: '1',
						area: '2',
						status: '1',
						pricePerHour: '60',
						tags: ['斯诺克专用', '比赛台'],
						description: '标准斯诺克比赛台，配备专业黑八桌布，适合比赛和高水平训练使用。'
					};
					
					// 设置选择器的索引
					this.typeIndex = this.typeValues.indexOf(this.tableInfo.type);
					if (this.typeIndex === -1) this.typeIndex = 0;
					
					this.sizeIndex = this.sizeValues.indexOf(this.tableInfo.size);
					if (this.sizeIndex === -1) this.sizeIndex = 0;
					
					this.areaIndex = this.areaValues.indexOf(this.tableInfo.area);
					if (this.areaIndex === -1) this.areaIndex = 0;
					
					this.statusIndex = this.statusValues.indexOf(this.tableInfo.status);
					if (this.statusIndex === -1) this.statusIndex = 0;
					
					uni.hideLoading();
				}, 500);
			},
			typeChange(e) {
				this.typeIndex = e.detail.value;
				this.tableInfo.type = this.typeValues[this.typeIndex];
			},
			sizeChange(e) {
				this.sizeIndex = e.detail.value;
				this.tableInfo.size = this.sizeValues[this.sizeIndex];
			},
			areaChange(e) {
				this.areaIndex = e.detail.value;
				this.tableInfo.area = this.areaValues[this.areaIndex];
			},
			statusChange(e) {
				this.statusIndex = e.detail.value;
				this.tableInfo.status = this.statusValues[this.statusIndex];
			},
			switchToEdit() {
				this.viewMode = false;
				this.type = 'edit';
			},
			toggleTag(tag) {
				if (this.selectedTags.includes(tag)) {
					// 如果已选中，则移除
					const index = this.selectedTags.indexOf(tag);
					this.selectedTags.splice(index, 1);
					
					// 同时从标签中移除
					const tagIndex = this.tableInfo.tags.indexOf(tag);
					if (tagIndex !== -1) {
						this.tableInfo.tags.splice(tagIndex, 1);
					}
				} else {
					// 如果未选中，则添加
					this.selectedTags.push(tag);
					
					// 同时添加到标签中
					if (!this.tableInfo.tags.includes(tag)) {
						this.tableInfo.tags.push(tag);
					}
				}
			},
			showAddTagDialog() {
				this.newTag = '';
				this.dialogType = 'info';
				this.$refs.tagPopup.open();
			},
			closeDialog() {
				this.$refs.tagPopup.close();
			},
			addNewTag() {
				if (this.newTag.trim()) {
					// 检查是否已存在
					if (!this.tableInfo.tags.includes(this.newTag.trim())) {
						this.tableInfo.tags.push(this.newTag.trim());
						this.selectedTags.push(this.newTag.trim());
					}
				}
				this.closeDialog();
			},
			chooseImage() {
				if (this.viewMode) {
					return;
				}
				
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						this.tableInfo.image = res.tempFilePaths[0];
					}
				});
			},
			submitForm() {
				if (this.viewMode) {
					return;
				}
				
				// 表单验证
				if (!this.tableInfo.name) {
					uni.showToast({
						title: '请输入台球桌名称',
						icon: 'none'
					});
					return;
				}
				
				if (!this.tableInfo.pricePerHour) {
					uni.showToast({
						title: '请输入每小时收费',
						icon: 'none'
					});
					return;
				}
				
				// 提交表单
				uni.showLoading({
					title: '保存中'
				});
				
				// 模拟API请求延迟
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
					
					// 保存成功后返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}, 1000);
			},
			goBack() {
				uni.navigateBack();
			},
			showQRCode() {
				this.$refs.qrcodePopup.open();
			},
			closeQRCode() {
				this.$refs.qrcodePopup.close();
			},
			saveQRCode() {
				uni.saveImageToPhotosAlbum({
					filePath: '/static/images/qrcode.png',
					success: () => {
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						});
					},
					fail: () => {
						uni.showToast({
							title: '保存失败，请检查权限',
							icon: 'none'
						});
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.detail-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 150rpx;
	}
	
	.header-card {
		background-color: #FFFFFF;
		padding: 40rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.avatar-box {
		position: relative;
		margin-bottom: 20rpx;
	}
	
	.table-image {
		width: 200rpx;
		height: 140rpx;
		border-radius: 10rpx;
		background-color: #F5F5F5;
	}
	
	.upload-btn {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 50rpx;
		height: 50rpx;
		border-radius: 25rpx;
		background-color: #007AFF;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.base-info {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.table-name {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
		text-align: center;
	}
	
	.status-tag {
		font-size: 24rpx;
		padding: 4rpx 20rpx;
		border-radius: 20rpx;
	}
	
	.status-available {
		background-color: #EBFFEC;
		color: #00CC66;
	}
	
	.status-occupied {
		background-color: #FFF3E0;
		color: #FF9500;
	}
	
	.info-card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		margin: 0 20rpx 20rpx;
		padding: 30rpx;
	}
	
	.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
		border-left: 8rpx solid #007AFF;
		padding-left: 20rpx;
	}
	
	.form-item {
		margin-bottom: 20rpx;
	}
	
	.form-label {
		font-size: 28rpx;
		color: #666666;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.form-content {
		background-color: #F5F5F5;
		padding: 20rpx;
		border-radius: 8rpx;
	}
	
	.form-content input {
		font-size: 28rpx;
		color: #333333;
	}
	
	.picker-view {
		font-size: 28rpx;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.tags-container {
		display: flex;
		flex-wrap: wrap;
	}
	
	.tag-item {
		padding: 10rpx 20rpx;
		background-color: #F5F5F5;
		color: #666666;
		font-size: 26rpx;
		border-radius: 6rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.tag-item.active {
		background-color: #E3F8FF;
		color: #007AFF;
	}
	
	.add-tag {
		background-color: #F0F7FF;
		color: #007AFF;
		display: flex;
		align-items: center;
	}
	
	.form-textarea {
		width: 100%;
		height: 240rpx;
		background-color: #F5F5F5;
		padding: 20rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #333333;
		box-sizing: border-box;
	}
	
	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #FFFFFF;
		padding: 20rpx;
		display: flex;
		justify-content: space-between;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.action-btn {
		flex: 1;
		margin: 0 10rpx;
		border-radius: 40rpx;
		padding: 20rpx 0;
		font-size: 28rpx;
		text-align: center;
	}
	
	.cancel-btn {
		background-color: #F5F5F5;
		color: #666666;
	}
	
	.primary-btn {
		background-color: #007AFF;
		color: #FFFFFF;
	}
	
	.edit-btn {
		background-color: rgba(0, 122, 255, 0.1);
		color: #007AFF;
	}
	
	.qrcode-btn {
		background-color: rgba(0, 204, 102, 0.1);
		color: #00CC66;
	}
	
	.empty-tip {
		font-size: 28rpx;
		color: #999999;
		text-align: center;
		padding: 20rpx 0;
	}
	
	.qrcode-container {
		background-color: #FFFFFF;
		width: 580rpx;
		border-radius: 16rpx;
		overflow: hidden;
		padding-bottom: 30rpx;
	}
	
	.qrcode-title {
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #EEEEEE;
	}
	
	.qrcode-content {
		padding: 40rpx;
		display: flex;
		justify-content: center;
	}
	
	.qrcode-image {
		width: 400rpx;
		height: 400rpx;
	}
	
	.qrcode-tips {
		font-size: 26rpx;
		color: #999999;
		text-align: center;
		margin-bottom: 30rpx;
	}
	
	.qrcode-actions {
		display: flex;
		justify-content: center;
		padding: 0 40rpx;
	}
	
	.qrcode-btn {
		flex: 1;
		height: 80rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		margin: 0 10rpx;
	}
	
	.save-btn {
		background-color: #007AFF;
		color: #FFFFFF;
	}
	
	.close-btn {
		background-color: #F5F5F5;
		color: #666666;
	}
</style> 