{"version": 3, "sources": ["../src/index.js"], "names": ["contain", "w", "h", "alignBits", "mode", "cb", "throwError", "call", "constructor", "HORIZONTAL_ALIGN_CENTER", "VERTICAL_ALIGN_MIDDLE", "hbits", "vbits", "alignH", "alignV", "f", "bitmap", "width", "height", "c", "clone<PERSON>uiet", "scale", "resize", "scanQuiet", "x", "y", "idx", "data", "writeUInt32BE", "_background", "blit"], "mappings": ";;;;;;;AAAA;;AAEA;;;;;;;;;eASe;AAAA,SAAO;AACpBA,IAAAA,OADoB,mBACZC,CADY,EACTC,CADS,EACNC,SADM,EACKC,IADL,EACWC,EADX,EACe;AACjC,UAAI,OAAOJ,CAAP,KAAa,QAAb,IAAyB,OAAOC,CAAP,KAAa,QAA1C,EAAoD;AAClD,eAAOI,kBAAWC,IAAX,CAAgB,IAAhB,EAAsB,yBAAtB,EAAiDF,EAAjD,CAAP;AACD,OAHgC,CAKjC;;;AACA,UAAI,OAAOF,SAAP,KAAqB,QAAzB,EAAmC;AACjC,YAAI,OAAOC,IAAP,KAAgB,UAAhB,IAA8B,OAAOC,EAAP,KAAc,WAAhD,EAA6DA,EAAE,GAAGD,IAAL;AAC7DA,QAAAA,IAAI,GAAGD,SAAP;AACAA,QAAAA,SAAS,GAAG,IAAZ;AACD;;AAED,UAAI,OAAOA,SAAP,KAAqB,UAAzB,EAAqC;AACnC,YAAI,OAAOE,EAAP,KAAc,WAAlB,EAA+BA,EAAE,GAAGF,SAAL;AAC/BC,QAAAA,IAAI,GAAG,IAAP;AACAD,QAAAA,SAAS,GAAG,IAAZ;AACD;;AAED,UAAI,OAAOC,IAAP,KAAgB,UAAhB,IAA8B,OAAOC,EAAP,KAAc,WAAhD,EAA6D;AAC3DA,QAAAA,EAAE,GAAGD,IAAL;AACAA,QAAAA,IAAI,GAAG,IAAP;AACD;;AAEDD,MAAAA,SAAS,GACPA,SAAS,IACT,KAAKK,WAAL,CAAiBC,uBAAjB,GACE,KAAKD,WAAL,CAAiBE,qBAHrB;AAIA,UAAMC,KAAK,GAAGR,SAAS,GAAI,CAAC,KAAK,CAAN,IAAW,CAAtC;AACA,UAAMS,KAAK,GAAGT,SAAS,IAAI,CAA3B,CA5BiC,CA8BjC;;AACA,UACE,EACGQ,KAAK,KAAK,CAAV,IAAe,EAAEA,KAAK,GAAIA,KAAK,GAAG,CAAnB,CAAhB,IACCC,KAAK,KAAK,CAAV,IAAe,EAAEA,KAAK,GAAIA,KAAK,GAAG,CAAnB,CAFlB,CADF,EAKE;AACA,eAAON,kBAAWC,IAAX,CACL,IADK,EAEL,2CAFK,EAGLF,EAHK,CAAP;AAKD;;AAED,UAAMQ,MAAM,GAAGF,KAAK,IAAI,CAAxB,CA5CiC,CA4CN;;AAC3B,UAAMG,MAAM,GAAGF,KAAK,IAAI,CAAxB,CA7CiC,CA6CN;;AAE3B,UAAMG,CAAC,GACLd,CAAC,GAAGC,CAAJ,GAAQ,KAAKc,MAAL,CAAYC,KAAZ,GAAoB,KAAKD,MAAL,CAAYE,MAAxC,GACIhB,CAAC,GAAG,KAAKc,MAAL,CAAYE,MADpB,GAEIjB,CAAC,GAAG,KAAKe,MAAL,CAAYC,KAHtB;AAIA,UAAME,CAAC,GAAG,KAAKC,UAAL,GAAkBC,KAAlB,CAAwBN,CAAxB,EAA2BX,IAA3B,CAAV;AAEA,WAAKkB,MAAL,CAAYrB,CAAZ,EAAeC,CAAf,EAAkBE,IAAlB;AACA,WAAKmB,SAAL,CAAe,CAAf,EAAkB,CAAlB,EAAqB,KAAKP,MAAL,CAAYC,KAAjC,EAAwC,KAAKD,MAAL,CAAYE,MAApD,EAA4D,UAC1DM,CAD0D,EAE1DC,CAF0D,EAG1DC,GAH0D,EAI1D;AACA,aAAKV,MAAL,CAAYW,IAAZ,CAAiBC,aAAjB,CAA+B,KAAKC,WAApC,EAAiDH,GAAjD;AACD,OAND;AAOA,WAAKI,IAAL,CACEX,CADF,EAEG,CAAC,KAAKH,MAAL,CAAYC,KAAZ,GAAoBE,CAAC,CAACH,MAAF,CAASC,KAA9B,IAAuC,CAAxC,GAA6CJ,MAF/C,EAGG,CAAC,KAAKG,MAAL,CAAYE,MAAZ,GAAqBC,CAAC,CAACH,MAAF,CAASE,MAA/B,IAAyC,CAA1C,GAA+CJ,MAHjD;;AAMA,UAAI,0BAAcT,EAAd,CAAJ,EAAuB;AACrBA,QAAAA,EAAE,CAACE,IAAH,CAAQ,IAAR,EAAc,IAAd,EAAoB,IAApB;AACD;;AAED,aAAO,IAAP;AACD;AAzEmB,GAAP;AAAA,C", "sourcesContent": ["import { isNodePattern, throwError } from '@jimp/utils';\n\n/**\n * Scale the image to the given width and height keeping the aspect ratio. Some parts of the image may be letter boxed.\n * @param {number} w the width to resize the image to\n * @param {number} h the height to resize the image to\n * @param {number} alignBits (optional) A bitmask for horizontal and vertical alignment\n * @param {string} mode (optional) a scaling method (e.g. Jimp.RESIZE_BEZIER)\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {<PERSON><PERSON>} this for chaining of methods\n */\nexport default () => ({\n  contain(w, h, alignBits, mode, cb) {\n    if (typeof w !== 'number' || typeof h !== 'number') {\n      return throwError.call(this, 'w and h must be numbers', cb);\n    }\n\n    // permit any sort of optional parameters combination\n    if (typeof alignBits === 'string') {\n      if (typeof mode === 'function' && typeof cb === 'undefined') cb = mode;\n      mode = alignBits;\n      alignBits = null;\n    }\n\n    if (typeof alignBits === 'function') {\n      if (typeof cb === 'undefined') cb = alignBits;\n      mode = null;\n      alignBits = null;\n    }\n\n    if (typeof mode === 'function' && typeof cb === 'undefined') {\n      cb = mode;\n      mode = null;\n    }\n\n    alignBits =\n      alignBits ||\n      this.constructor.HORIZONTAL_ALIGN_CENTER |\n        this.constructor.VERTICAL_ALIGN_MIDDLE;\n    const hbits = alignBits & ((1 << 3) - 1);\n    const vbits = alignBits >> 3;\n\n    // check if more flags than one is in the bit sets\n    if (\n      !(\n        (hbits !== 0 && !(hbits & (hbits - 1))) ||\n        (vbits !== 0 && !(vbits & (vbits - 1)))\n      )\n    ) {\n      return throwError.call(\n        this,\n        'only use one flag per alignment direction',\n        cb\n      );\n    }\n\n    const alignH = hbits >> 1; // 0, 1, 2\n    const alignV = vbits >> 1; // 0, 1, 2\n\n    const f =\n      w / h > this.bitmap.width / this.bitmap.height\n        ? h / this.bitmap.height\n        : w / this.bitmap.width;\n    const c = this.cloneQuiet().scale(f, mode);\n\n    this.resize(w, h, mode);\n    this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, function(\n      x,\n      y,\n      idx\n    ) {\n      this.bitmap.data.writeUInt32BE(this._background, idx);\n    });\n    this.blit(\n      c,\n      ((this.bitmap.width - c.bitmap.width) / 2) * alignH,\n      ((this.bitmap.height - c.bitmap.height) / 2) * alignV\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  }\n});\n"], "file": "index.js"}