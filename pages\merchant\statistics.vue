<template>
	<view class="statistics-container">
		<!-- 时间范围选择 -->
		<view class="date-filter">
			<view class="tab-group">
				<view 
					class="tab-item" 
					v-for="(item, index) in dateRanges" 
					:key="index"
					:class="{ active: currentRangeIndex === index }"
					@click="selectDateRange(index)"
				>
					{{item.name}}
				</view>
			</view>
			
			<view class="custom-date" @click="showDatePicker = true">
				<text class="date-text">{{currentDateRange}}</text>
				<uni-icons type="calendar" size="16" color="#007AFF"></uni-icons>
			</view>
		</view>
		
		<!-- 数据总览卡片 -->
		<view class="overview-card">
			<view class="card-title">数据总览</view>
			<view class="overview-data">
				<view class="data-item">
					<text class="data-value">{{statisticsData.totalIncome.toFixed(2)}}</text>
					<text class="data-label">营业收入（元）</text>
				</view>
				<view class="data-item">
					<text class="data-value">{{statisticsData.orderCount}}</text>
					<text class="data-label">订单数量</text>
				</view>
				<view class="data-item">
					<text class="data-value">{{calculateAverageIncome()}}</text>
					<text class="data-label">客单价（元）</text>
				</view>
			</view>
		</view>
		
		<!-- 收入占比卡片 -->
		<view class="chart-card">
			<view class="card-title">
				<text>收入占比</text>
				<view class="chart-legend">
					<view class="legend-item">
						<view class="legend-color" style="background-color: #007AFF;"></view>
						<text class="legend-text">台球桌</text>
					</view>
					<view class="legend-item">
						<view class="legend-color" style="background-color: #FF9500;"></view>
						<text class="legend-text">教练课程</text>
					</view>
					<view class="legend-item">
						<view class="legend-color" style="background-color: #4CD964;"></view>
						<text class="legend-text">其他</text>
					</view>
				</view>
			</view>
			
			<view class="pie-chart-container">
				<!-- 这里假设使用图表组件，实际项目中可替换为具体的图表库 -->
				<view class="pie-chart">
					<view class="pie-chart-placeholder">
						<image class="chart-image" src="/static/images/pie-chart.png" mode="widthFix"></image>
					</view>
				</view>
				
				<view class="pie-data">
					<view class="pie-data-item">
						<text class="pie-data-label">台球桌</text>
						<text class="pie-data-value">{{statisticsData.tableIncome.toFixed(2)}}元</text>
						<text class="pie-data-percent">{{calculatePercent(statisticsData.tableIncome, statisticsData.totalIncome)}}%</text>
					</view>
					<view class="pie-data-item">
						<text class="pie-data-label">教练课程</text>
						<text class="pie-data-value">{{statisticsData.coachIncome.toFixed(2)}}元</text>
						<text class="pie-data-percent">{{calculatePercent(statisticsData.coachIncome, statisticsData.totalIncome)}}%</text>
					</view>
					<view class="pie-data-item">
						<text class="pie-data-label">其他</text>
						<text class="pie-data-value">{{statisticsData.otherIncome.toFixed(2)}}元</text>
						<text class="pie-data-percent">{{calculatePercent(statisticsData.otherIncome, statisticsData.totalIncome)}}%</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 收入趋势卡片 -->
		<view class="chart-card">
			<view class="card-title">收入趋势</view>
			
			<view class="line-chart-container">
				<!-- 这里假设使用图表组件，实际项目中可替换为具体的图表库 -->
				<view class="line-chart">
					<view class="line-chart-placeholder">
						<image class="chart-image" src="/static/images/line-chart.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 热门服务卡片 -->
		<view class="list-card">
			<view class="card-title">热门服务排行</view>
			
			<view class="rank-list">
				<view class="rank-item" v-for="(item, index) in statisticsData.popularServices" :key="index">
					<view class="rank-num" :class="index < 3 ? 'top-rank' : ''">{{index + 1}}</view>
					<view class="rank-info">
						<text class="rank-name">{{item.name}}</text>
						<text class="rank-desc">{{item.type === 'table' ? '台球桌' : item.type === 'coach' ? '教练课程' : '其他服务'}}</text>
					</view>
					<view class="rank-data">
						<text class="rank-value">{{item.income.toFixed(2)}}元</text>
						<text class="rank-count">{{item.count}}次</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 营业时段分析卡片 -->
		<view class="chart-card">
			<view class="card-title">营业时段分析</view>
			
			<view class="bar-chart-container">
				<!-- 这里假设使用图表组件，实际项目中可替换为具体的图表库 -->
				<view class="bar-chart">
					<view class="bar-chart-placeholder">
						<image class="chart-image" src="/static/images/bar-chart.png" mode="widthFix"></image>
					</view>
				</view>
				
				<view class="time-analysis">
					<view class="time-item">
						<text class="time-label">高峰时段</text>
						<text class="time-value">{{statisticsData.peakHours}}</text>
					</view>
					<view class="time-item">
						<text class="time-label">平均使用时长</text>
						<text class="time-value">{{statisticsData.averageDuration}}小时</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 日期选择器弹窗 -->
		<uni-calendar 
			:insert="false"
			:start-date="startDate"
			:end-date="endDate"
			:range="true"
			v-model="showDatePicker" 
			@confirm="dateConfirm"
		/>
	</view>
</template>

<script>
	export default {
		data() {
			const today = new Date();
			const year = today.getFullYear();
			const month = today.getMonth();
			const day = today.getDate();
			
			// 计算本周的开始和结束日期（周一到周日）
			const weekStart = new Date(today);
			const dayOfWeek = today.getDay() || 7; // 转换为周一到周日（1-7）
			weekStart.setDate(today.getDate() - dayOfWeek + 1);
			const weekEnd = new Date(weekStart);
			weekEnd.setDate(weekStart.getDate() + 6);
			
			// 计算本月的开始和结束日期
			const monthStart = new Date(year, month, 1);
			const monthEnd = new Date(year, month + 1, 0);
			
			return {
				// 日期范围选项
				dateRanges: [
					{
						name: '今日',
						startDate: this.formatDate(today),
						endDate: this.formatDate(today)
					},
					{
						name: '本周',
						startDate: this.formatDate(weekStart),
						endDate: this.formatDate(weekEnd)
					},
					{
						name: '本月',
						startDate: this.formatDate(monthStart),
						endDate: this.formatDate(monthEnd)
					},
					{
						name: '自定义',
						startDate: '',
						endDate: ''
					}
				],
				currentRangeIndex: 2, // 默认选择本月
				
				// 日期选择器
				showDatePicker: false,
				startDate: '', // 日历开始日期
				endDate: '', // 日历结束日期
				
				// 统计数据
				statisticsData: {
					totalIncome: 0,
					tableIncome: 0,
					coachIncome: 0,
					otherIncome: 0,
					orderCount: 0,
					peakHours: '18:00-22:00',
					averageDuration: 2.5,
					popularServices: []
				}
			}
		},
		computed: {
			currentDateRange() {
				const range = this.dateRanges[this.currentRangeIndex];
				return `${range.startDate} 至 ${range.endDate}`;
			}
		},
		onLoad() {
			// 加载统计数据
			this.loadStatisticsData();
		},
		methods: {
			formatDate(date) {
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			selectDateRange(index) {
				this.currentRangeIndex = index;
				
				if (index === 3 && !this.dateRanges[3].startDate) {
					// 如果选择了自定义但还没有设置日期，弹出日期选择器
					this.showDatePicker = true;
				} else {
					// 加载选定日期范围的数据
					this.loadStatisticsData();
				}
			},
			dateConfirm(e) {
				// 设置自定义日期范围
				this.dateRanges[3].startDate = e.range.data[0];
				this.dateRanges[3].endDate = e.range.data[e.range.data.length - 1];
				this.currentRangeIndex = 3;
				this.showDatePicker = false;
				
				// 加载自定义日期范围的数据
				this.loadStatisticsData();
			},
			loadStatisticsData() {
				// 显示加载中
				uni.showLoading({
					title: '加载中'
				});
				
				// 这里使用模拟数据，实际项目中应该从API获取
				setTimeout(() => {
					// 根据不同的日期范围设置不同的数据
					if (this.currentRangeIndex === 0) {
						// 今日数据
						this.statisticsData = {
							totalIncome: 1580.50,
							tableIncome: 980.00,
							coachIncome: 500.00,
							otherIncome: 100.50,
							orderCount: 15,
							peakHours: '19:00-22:00',
							averageDuration: 2.3,
							popularServices: [
								{ name: '黑八台', type: 'table', income: 580.00, count: 8 },
								{ name: '入门课程', type: 'coach', income: 300.00, count: 2 },
								{ name: '斯诺克台', type: 'table', income: 400.00, count: 4 },
								{ name: '台球杆租赁', type: 'other', income: 60.00, count: 6 },
								{ name: '进阶课程', type: 'coach', income: 200.00, count: 1 }
							]
						};
					} else if (this.currentRangeIndex === 1) {
						// 本周数据
						this.statisticsData = {
							totalIncome: 8950.75,
							tableIncome: 5800.25,
							coachIncome: 2500.00,
							otherIncome: 650.50,
							orderCount: 85,
							peakHours: '18:00-22:00',
							averageDuration: 2.5,
							popularServices: [
								{ name: '黑八台', type: 'table', income: 3200.00, count: 45 },
								{ name: '入门课程', type: 'coach', income: 1500.00, count: 10 },
								{ name: '斯诺克台', type: 'table', income: 2600.25, count: 28 },
								{ name: '进阶课程', type: 'coach', income: 1000.00, count: 5 },
								{ name: '台球杆租赁', type: 'other', income: 400.00, count: 40 }
							]
						};
					} else {
						// 本月数据 (默认) 或自定义日期范围数据
						this.statisticsData = {
							totalIncome: 35800.50,
							tableIncome: 22500.00,
							coachIncome: 10800.50,
							otherIncome: 2500.00,
							orderCount: 320,
							peakHours: '18:00-22:00',
							averageDuration: 2.5,
							popularServices: [
								{ name: '黑八台', type: 'table', income: 12500.00, count: 180 },
								{ name: '斯诺克台', type: 'table', income: 10000.00, count: 120 },
								{ name: '入门课程', type: 'coach', income: 6000.00, count: 40 },
								{ name: '进阶课程', type: 'coach', income: 4800.50, count: 20 },
								{ name: '台球杆租赁', type: 'other', income: 1500.00, count: 150 }
							]
						};
					}
					
					uni.hideLoading();
				}, 500);
			},
			calculateAverageIncome() {
				if (this.statisticsData.orderCount === 0) {
					return '0.00';
				}
				return (this.statisticsData.totalIncome / this.statisticsData.orderCount).toFixed(2);
			},
			calculatePercent(value, total) {
				if (total === 0) {
					return '0.0';
				}
				return ((value / total) * 100).toFixed(1);
			}
		}
	}
</script>

<style lang="scss">
	.statistics-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 30rpx;
	}
	
	.date-filter {
		background-color: #FFFFFF;
		padding: 20rpx 30rpx;
		display: flex;
		flex-direction: column;
		border-bottom: 1rpx solid #EEEEEE;
		position: sticky;
		top: 0;
		z-index: 100;
	}
	
	.tab-group {
		display: flex;
		background-color: #F5F5F5;
		border-radius: 8rpx;
		padding: 5rpx;
		margin-bottom: 20rpx;
	}
	
	.tab-item {
		flex: 1;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 26rpx;
		color: #666666;
		border-radius: 6rpx;
		
		&.active {
			background-color: #FFFFFF;
			color: #007AFF;
			box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
		}
	}
	
	.custom-date {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 10rpx 0;
	}
	
	.date-text {
		font-size: 24rpx;
		color: #007AFF;
		margin-right: 10rpx;
	}
	
	.overview-card {
		background-color: #FFFFFF;
		margin: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.card-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #F5F5F5;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.overview-data {
		display: flex;
		padding: 30rpx;
	}
	
	.data-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: relative;
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			right: 0;
			top: 10rpx;
			bottom: 10rpx;
			width: 1rpx;
			background-color: #EEEEEE;
		}
	}
	
	.data-value {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
	}
	
	.data-label {
		font-size: 24rpx;
		color: #999999;
	}
	
	.chart-card {
		background-color: #FFFFFF;
		margin: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.chart-legend {
		display: flex;
	}
	
	.legend-item {
		display: flex;
		align-items: center;
		margin-left: 20rpx;
	}
	
	.legend-color {
		width: 20rpx;
		height: 20rpx;
		border-radius: 4rpx;
		margin-right: 8rpx;
	}
	
	.legend-text {
		font-size: 22rpx;
		color: #666666;
		font-weight: normal;
	}
	
	.pie-chart-container, .line-chart-container, .bar-chart-container {
		padding: 20rpx 30rpx 30rpx;
	}
	
	.chart-image {
		width: 100%;
	}
	
	.pie-chart, .line-chart, .bar-chart {
		margin-bottom: 20rpx;
	}
	
	.pie-chart-placeholder, .line-chart-placeholder, .bar-chart-placeholder {
		display: flex;
		justify-content: center;
		padding: 20rpx 0;
	}
	
	.pie-data {
		background-color: #F9F9F9;
		border-radius: 8rpx;
		padding: 20rpx;
	}
	
	.pie-data-item {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
	
	.pie-data-label {
		width: 150rpx;
		font-size: 26rpx;
		color: #666666;
	}
	
	.pie-data-value {
		flex: 1;
		font-size: 26rpx;
		color: #333333;
		font-weight: 500;
	}
	
	.pie-data-percent {
		width: 100rpx;
		text-align: right;
		font-size: 26rpx;
		color: #999999;
	}
	
	.list-card {
		background-color: #FFFFFF;
		margin: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.rank-list {
		padding: 10rpx 0;
	}
	
	.rank-item {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		position: relative;
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			left: 30rpx;
			right: 30rpx;
			bottom: 0;
			height: 1rpx;
			background-color: #F5F5F5;
		}
	}
	
	.rank-num {
		width: 50rpx;
		height: 50rpx;
		border-radius: 25rpx;
		background-color: #F5F5F5;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		color: #999999;
		margin-right: 20rpx;
		
		&.top-rank {
			background-color: #007AFF;
			color: #FFFFFF;
		}
	}
	
	.rank-info {
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	.rank-name {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 5rpx;
	}
	
	.rank-desc {
		font-size: 24rpx;
		color: #999999;
	}
	
	.rank-data {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}
	
	.rank-value {
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 5rpx;
	}
	
	.rank-count {
		font-size: 24rpx;
		color: #999999;
	}
	
	.time-analysis {
		display: flex;
		justify-content: space-around;
		background-color: #F9F9F9;
		border-radius: 8rpx;
		padding: 20rpx;
	}
	
	.time-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.time-label {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 10rpx;
	}
	
	.time-value {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
	}
</style> 