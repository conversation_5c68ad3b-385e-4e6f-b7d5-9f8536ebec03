<template>
	<view class="pricing-container">
		<view class="tips-card">
			<uni-icons type="info" size="16" color="#007AFF"></uni-icons>
			<text class="tips-text">价格设置将应用于所有预约订单</text>
		</view>
		
		<!-- 台球桌价格设置 -->
		<view class="setting-card">
			<view class="card-title">台球桌价格</view>
			
			<view class="price-settings">
				<view class="price-item" v-for="(item, index) in tablePrices" :key="index">
					<view class="item-header">
						<text class="item-title">{{item.name}}</text>
						<switch :checked="item.enabled" @change="togglePrice(index, 'table')" color="#007AFF"/>
					</view>
					
					<view class="price-content" v-if="item.enabled">
						<view class="price-field">
							<text class="field-label">价格（元/小时）</text>
							<view class="input-box">
								<input type="digit" v-model="item.price" placeholder="请输入价格"/>
							</view>
						</view>
						
						<view class="price-field">
							<text class="field-label">会员价（元/小时）</text>
							<view class="input-box">
								<input type="digit" v-model="item.memberPrice" placeholder="请输入会员价"/>
							</view>
						</view>
						
						<view class="price-field">
							<text class="field-label">描述</text>
							<view class="input-box">
								<input type="text" v-model="item.description" placeholder="请输入描述"/>
							</view>
						</view>
					</view>
				</view>
				
				<view class="add-btn" @click="addTableType">
					<uni-icons type="plusempty" size="16" color="#007AFF"></uni-icons>
					<text>添加台球桌类型</text>
				</view>
			</view>
		</view>
		
		<!-- 教练课程价格设置 -->
		<view class="setting-card">
			<view class="card-title">教练课程价格</view>
			
			<view class="price-settings">
				<view class="price-item" v-for="(item, index) in coachPrices" :key="index">
					<view class="item-header">
						<text class="item-title">{{item.name}}</text>
						<switch :checked="item.enabled" @change="togglePrice(index, 'coach')" color="#007AFF"/>
					</view>
					
					<view class="price-content" v-if="item.enabled">
						<view class="price-field">
							<text class="field-label">价格（元/节）</text>
							<view class="input-box">
								<input type="digit" v-model="item.price" placeholder="请输入价格"/>
							</view>
						</view>
						
						<view class="price-field">
							<text class="field-label">会员价（元/节）</text>
							<view class="input-box">
								<input type="digit" v-model="item.memberPrice" placeholder="请输入会员价"/>
							</view>
						</view>
						
						<view class="price-field">
							<text class="field-label">时长（分钟/节）</text>
							<view class="input-box">
								<input type="number" v-model="item.duration" placeholder="请输入课程时长"/>
							</view>
						</view>
						
						<view class="price-field">
							<text class="field-label">描述</text>
							<view class="input-box">
								<input type="text" v-model="item.description" placeholder="请输入描述"/>
							</view>
						</view>
					</view>
				</view>
				
				<view class="add-btn" @click="addCoachCourse">
					<uni-icons type="plusempty" size="16" color="#007AFF"></uni-icons>
					<text>添加课程类型</text>
				</view>
			</view>
		</view>
		
		<!-- 其他收费项目 -->
		<view class="setting-card">
			<view class="card-title">其他收费项目</view>
			
			<view class="price-settings">
				<view class="price-item" v-for="(item, index) in otherPrices" :key="index">
					<view class="item-header">
						<text class="item-title">{{item.name}}</text>
						<view class="action-btns">
							<view class="delete-btn" @click="removePrice(index, 'other')">
								<uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
							</view>
							<switch :checked="item.enabled" @change="togglePrice(index, 'other')" color="#007AFF"/>
						</view>
					</view>
					
					<view class="price-content" v-if="item.enabled">
						<view class="price-field">
							<text class="field-label">价格（元）</text>
							<view class="input-box">
								<input type="digit" v-model="item.price" placeholder="请输入价格"/>
							</view>
						</view>
						
						<view class="price-field">
							<text class="field-label">会员价（元）</text>
							<view class="input-box">
								<input type="digit" v-model="item.memberPrice" placeholder="请输入会员价"/>
							</view>
						</view>
						
						<view class="price-field">
							<text class="field-label">描述</text>
							<view class="input-box">
								<input type="text" v-model="item.description" placeholder="请输入描述"/>
							</view>
						</view>
					</view>
				</view>
				
				<view class="add-item-form" v-if="showAddForm">
					<view class="form-title">添加收费项目</view>
					<view class="form-field">
						<text class="form-label">项目名称</text>
						<view class="input-box">
							<input type="text" v-model="newItem.name" placeholder="请输入项目名称"/>
						</view>
					</view>
					<view class="form-actions">
						<view class="cancel-btn" @click="cancelAddItem">取消</view>
						<view class="confirm-btn" @click="confirmAddItem">确定</view>
					</view>
				</view>
				
				<view class="add-btn" @click="showAddItemForm" v-if="!showAddForm">
					<uni-icons type="plusempty" size="16" color="#007AFF"></uni-icons>
					<text>添加收费项目</text>
				</view>
			</view>
		</view>
		
		<!-- 保存按钮 -->
		<view class="bottom-actions">
			<button class="save-btn" @click="saveSettings">保存</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 台球桌价格
				tablePrices: [
					{
						id: 1,
						name: '黑八台',
						price: 60,
						memberPrice: 50,
						description: '标准黑八台球桌',
						enabled: true
					},
					{
						id: 2,
						name: '花式台',
						price: 80,
						memberPrice: 65,
						description: '专业花式台球桌',
						enabled: true
					},
					{
						id: 3,
						name: '斯诺克台',
						price: 100,
						memberPrice: 80,
						description: '标准斯诺克台',
						enabled: true
					}
				],
				
				// 教练课程价格
				coachPrices: [
					{
						id: 1,
						name: '入门课程',
						price: 200,
						memberPrice: 180,
						duration: 60,
						description: '适合初学者的基础教学',
						enabled: true
					},
					{
						id: 2,
						name: '进阶课程',
						price: 300,
						memberPrice: 260,
						duration: 90,
						description: '适合有一定基础的学员',
						enabled: true
					},
					{
						id: 3,
						name: 'VIP专业课程',
						price: 500,
						memberPrice: 450,
						duration: 120,
						description: '专业教练一对一指导',
						enabled: true
					}
				],
				
				// 其他收费项目
				otherPrices: [
					{
						id: 1,
						name: '台球杆租赁',
						price: 20,
						memberPrice: 15,
						description: '普通台球杆',
						enabled: true
					},
					{
						id: 2,
						name: '饮料',
						price: 10,
						memberPrice: 8,
						description: '矿泉水、可乐等',
						enabled: true
					}
				],
				
				// 添加项目表单
				showAddForm: false,
				newItem: {
					name: '',
					price: '',
					memberPrice: '',
					description: ''
				}
			}
		},
		onLoad() {
			// 加载价格设置
			this.loadPriceSettings();
		},
		methods: {
			loadPriceSettings() {
				// 从存储加载价格设置
				const settings = uni.getStorageSync('priceSettings');
				if (settings) {
					const data = JSON.parse(settings);
					this.tablePrices = data.tablePrices || this.tablePrices;
					this.coachPrices = data.coachPrices || this.coachPrices;
					this.otherPrices = data.otherPrices || this.otherPrices;
				}
			},
			togglePrice(index, type) {
				if (type === 'table') {
					this.tablePrices[index].enabled = !this.tablePrices[index].enabled;
				} else if (type === 'coach') {
					this.coachPrices[index].enabled = !this.coachPrices[index].enabled;
				} else if (type === 'other') {
					this.otherPrices[index].enabled = !this.otherPrices[index].enabled;
				}
			},
			addTableType() {
				this.tablePrices.push({
					id: this.getNewId(this.tablePrices),
					name: '新台球桌',
					price: 80,
					memberPrice: 70,
					description: '',
					enabled: true
				});
			},
			addCoachCourse() {
				this.coachPrices.push({
					id: this.getNewId(this.coachPrices),
					name: '新课程',
					price: 200,
					memberPrice: 180,
					duration: 60,
					description: '',
					enabled: true
				});
			},
			showAddItemForm() {
				this.showAddForm = true;
				this.newItem = {
					name: '',
					price: '',
					memberPrice: '',
					description: ''
				};
			},
			cancelAddItem() {
				this.showAddForm = false;
			},
			confirmAddItem() {
				if (!this.newItem.name) {
					uni.showToast({
						title: '请输入项目名称',
						icon: 'none'
					});
					return;
				}
				
				this.otherPrices.push({
					id: this.getNewId(this.otherPrices),
					name: this.newItem.name,
					price: this.newItem.price || 0,
					memberPrice: this.newItem.memberPrice || 0,
					description: this.newItem.description || '',
					enabled: true
				});
				
				this.showAddForm = false;
			},
			removePrice(index, type) {
				if (type === 'other') {
					this.otherPrices.splice(index, 1);
				}
			},
			getNewId(array) {
				let maxId = 0;
				array.forEach(item => {
					if (item.id > maxId) {
						maxId = item.id;
					}
				});
				return maxId + 1;
			},
			validatePriceSettings() {
				// 验证价格设置
				let isValid = true;
				
				// 验证台球桌价格
				for (let item of this.tablePrices) {
					if (item.enabled) {
						if (!item.name) {
							uni.showToast({
								title: '请输入台球桌名称',
								icon: 'none'
							});
							isValid = false;
							break;
						}
						
						if (isNaN(parseFloat(item.price)) || parseFloat(item.price) < 0) {
							uni.showToast({
								title: '请输入正确的台球桌价格',
								icon: 'none'
							});
							isValid = false;
							break;
						}
					}
				}
				
				// 验证教练课程价格
				for (let item of this.coachPrices) {
					if (item.enabled) {
						if (!item.name) {
							uni.showToast({
								title: '请输入课程名称',
								icon: 'none'
							});
							isValid = false;
							break;
						}
						
						if (isNaN(parseFloat(item.price)) || parseFloat(item.price) < 0) {
							uni.showToast({
								title: '请输入正确的课程价格',
								icon: 'none'
							});
							isValid = false;
							break;
						}
						
						if (isNaN(parseInt(item.duration)) || parseInt(item.duration) <= 0) {
							uni.showToast({
								title: '请输入正确的课程时长',
								icon: 'none'
							});
							isValid = false;
							break;
						}
					}
				}
				
				// 验证其他收费项目
				for (let item of this.otherPrices) {
					if (item.enabled) {
						if (!item.name) {
							uni.showToast({
								title: '请输入收费项目名称',
								icon: 'none'
							});
							isValid = false;
							break;
						}
						
						if (isNaN(parseFloat(item.price)) || parseFloat(item.price) < 0) {
							uni.showToast({
								title: '请输入正确的收费项目价格',
								icon: 'none'
							});
							isValid = false;
							break;
						}
					}
				}
				
				return isValid;
			},
			saveSettings() {
				// 验证价格设置
				if (!this.validatePriceSettings()) {
					return;
				}
				
				// 保存价格设置
				uni.setStorageSync('priceSettings', JSON.stringify({
					tablePrices: this.tablePrices,
					coachPrices: this.coachPrices,
					otherPrices: this.otherPrices
				}));
				
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
				
				// 返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		}
	}
</script>

<style lang="scss">
	.pricing-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding: 20rpx;
		padding-bottom: 150rpx;
	}
	
	.tips-card {
		display: flex;
		align-items: center;
		background-color: #F0F7FF;
		padding: 20rpx;
		border-radius: 8rpx;
		margin-bottom: 20rpx;
	}
	
	.tips-text {
		font-size: 24rpx;
		color: #007AFF;
		margin-left: 10rpx;
	}
	
	.setting-card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 20rpx;
	}
	
	.card-title {
		padding: 30rpx;
		border-bottom: 1rpx solid #F5F5F5;
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.price-settings {
		padding: 20rpx 30rpx;
	}
	
	.price-item {
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx dashed #EEEEEE;
		
		&:last-child {
			margin-bottom: 0;
			border-bottom: none;
		}
	}
	
	.item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.item-title {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
	}
	
	.action-btns {
		display: flex;
		align-items: center;
	}
	
	.delete-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.price-content {
		background-color: #F9F9F9;
		border-radius: 8rpx;
		padding: 20rpx;
	}
	
	.price-field {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
	
	.field-label {
		width: 200rpx;
		font-size: 26rpx;
		color: #666666;
	}
	
	.input-box {
		flex: 1;
	}
	
	.input-box input {
		height: 60rpx;
		background-color: #FFFFFF;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 26rpx;
		color: #333333;
	}
	
	.add-btn {
		height: 80rpx;
		background-color: #F0F7FF;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 26rpx;
		color: #007AFF;
		margin-top: 20rpx;
		
		text {
			margin-left: 10rpx;
		}
	}
	
	.add-item-form {
		background-color: #F9F9F9;
		border-radius: 8rpx;
		padding: 20rpx;
		margin-top: 20rpx;
	}
	
	.form-title {
		font-size: 26rpx;
		color: #333333;
		font-weight: 500;
		margin-bottom: 20rpx;
	}
	
	.form-field {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.form-label {
		width: 150rpx;
		font-size: 26rpx;
		color: #666666;
	}
	
	.form-actions {
		display: flex;
		justify-content: flex-end;
		margin-top: 20rpx;
	}
	
	.cancel-btn, .confirm-btn {
		padding: 10rpx 30rpx;
		border-radius: 30rpx;
		font-size: 26rpx;
		margin-left: 20rpx;
	}
	
	.cancel-btn {
		background-color: #F5F5F5;
		color: #666666;
	}
	
	.confirm-btn {
		background-color: #007AFF;
		color: #FFFFFF;
	}
	
	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #FFFFFF;
		padding: 20rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.save-btn {
		background-color: #007AFF;
		color: #FFFFFF;
		border-radius: 40rpx;
		padding: 20rpx 0;
		font-size: 30rpx;
		text-align: center;
	}
</style> 