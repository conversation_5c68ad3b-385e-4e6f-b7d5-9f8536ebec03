<template>
  <i :class="iconClass" :style="iconStyle"></i>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    required: true
  },
  size: {
    type: [String, Number],
    default: 16
  },
  color: {
    type: String,
    default: '#333'
  }
})

const iconClass = computed(() => {
  const iconMap = {
    'person': 'icon-person',
    'locked': 'icon-locked',
    'eye': 'icon-eye',
    'eye-slash': 'icon-eye-slash',
    'right': 'icon-right',
    'search': 'icon-search',
    'calendar': 'icon-calendar',
    'phone': 'icon-phone',
    'phone-filled': 'icon-phone-filled',
    'checkmarkempty': 'icon-checkmark',
    'closeempty': 'icon-close',
    'flag': 'icon-flag',
    'gear': 'icon-gear',
    'chart': 'icon-chart',
    'contact': 'icon-contact',
    'shop': 'icon-shop',
    'headphones': 'icon-headphones',
    'gift': 'icon-gift',
    'rmb': 'icon-rmb',
    'location': 'icon-location',
    'camera': 'icon-camera',
    'checkbox': 'icon-checkbox',
    'plusempty': 'icon-plus'
  }
  
  return iconMap[props.type] || 'icon-default'
})

const iconStyle = computed(() => {
  return {
    fontSize: typeof props.size === 'number' ? `${props.size}px` : props.size,
    color: props.color,
    display: 'inline-block',
    fontFamily: 'iconfont'
  }
})
</script>

<style>
/* 简单的图标字体样式 */
@font-face {
  font-family: 'iconfont';
  src: url('data:application/x-font-woff2;charset=utf-8;base64,') format('woff2');
}

.icon-person::before { content: '👤'; }
.icon-locked::before { content: '🔒'; }
.icon-eye::before { content: '👁'; }
.icon-eye-slash::before { content: '🙈'; }
.icon-right::before { content: '▶'; }
.icon-search::before { content: '🔍'; }
.icon-calendar::before { content: '📅'; }
.icon-phone::before { content: '📞'; }
.icon-phone-filled::before { content: '☎'; }
.icon-checkmark::before { content: '✓'; }
.icon-close::before { content: '✕'; }
.icon-flag::before { content: '🏁'; }
.icon-gear::before { content: '⚙'; }
.icon-chart::before { content: '📊'; }
.icon-contact::before { content: '👥'; }
.icon-shop::before { content: '🏪'; }
.icon-headphones::before { content: '🎧'; }
.icon-gift::before { content: '🎁'; }
.icon-rmb::before { content: '💰'; }
.icon-location::before { content: '📍'; }
.icon-camera::before { content: '📷'; }
.icon-checkbox::before { content: '☑'; }
.icon-plus::before { content: '+'; }
.icon-default::before { content: '•'; }

[class*="icon-"] {
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}
</style> 