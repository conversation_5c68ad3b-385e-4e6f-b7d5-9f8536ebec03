<template>
	<view class="appointment-container">
		<!-- 状态过滤条 -->
		<view class="status-filter">
			<view class="filter-item" :class="{active: activeFilter === 'all'}" @click="filterAppointments('all')">
				全部 ({{getTotalCount()}})
			</view>
			<view class="filter-item" :class="{active: activeFilter === 'pending'}" @click="filterAppointments('pending')">
				待确认 ({{getCountByStatus('0')}})
			</view>
			<view class="filter-item" :class="{active: activeFilter === 'confirmed'}" @click="filterAppointments('confirmed')">
				已确认 ({{getCountByStatus('1')}})
			</view>
			<view class="filter-item" :class="{active: activeFilter === 'completed'}" @click="filterAppointments('completed')">
				已完成 ({{getCountByStatus('2')}})
			</view>
			<view class="filter-item" :class="{active: activeFilter === 'canceled'}" @click="filterAppointments('canceled')">
				已取消 ({{getCountByStatus('3')}})
			</view>
		</view>
		
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input-box">
				<uni-icons type="search" size="18" color="#999"></uni-icons>
				<input class="search-input" type="text" v-model="searchKey" placeholder="搜索订单号或用户名" @confirm="searchAppointments"/>
			</view>
			<view class="date-filter" @click="showCalendar = true">
				<text class="date-text">{{dateText}}</text>
				<uni-icons type="calendar" size="18" color="#007AFF"></uni-icons>
			</view>
		</view>
		
		<!-- 预约列表 -->
		<view class="appointment-list">
			<view class="appointment-item" v-for="(appointment, index) in filteredAppointments" :key="index" @click="navigateTo('/pages/appointment/detail?id='+appointment.id)">
				<view class="appointment-header">
					<view class="appointment-type" :class="appointment.type === 'coach' ? 'type-coach' : 'type-table'">
						{{appointment.type === 'coach' ? '教练' : '台球桌'}}
					</view>
					<view class="appointment-id">订单号: {{appointment.orderNo}}</view>
					<view class="appointment-status" :class="'status-'+appointment.status">
						{{getStatusText(appointment.status)}}
					</view>
				</view>
				
				<view class="appointment-content">
					<view class="appointment-info">
						<view class="info-item">
							<text class="label">用户:</text>
							<text class="value">{{appointment.userName}}</text>
						</view>
						<view class="info-item">
							<text class="label">预约对象:</text>
							<text class="value">{{appointment.type === 'coach' ? appointment.coachName : appointment.tableName}}</text>
						</view>
						<view class="info-item">
							<text class="label">预约时间:</text>
							<text class="value">{{appointment.appointmentDate}} {{appointment.startTime}}-{{appointment.endTime}}</text>
						</view>
						<view class="info-item">
							<text class="label">金额:</text>
							<text class="value price">¥{{appointment.totalAmount}}</text>
						</view>
					</view>
				</view>
				
				<view class="appointment-actions">
					<view class="action-btn" @click.stop="callUser(appointment)" v-if="appointment.userPhone">
						<uni-icons type="phone-filled" size="16" color="#007AFF"></uni-icons>
						<text class="action-text">联系用户</text>
					</view>
					<view class="action-btn confirm-btn" @click.stop="confirmAppointment(appointment)" v-if="appointment.status === '0'">
						<uni-icons type="checkmarkempty" size="16" color="#00CC66"></uni-icons>
						<text class="action-text">确认</text>
					</view>
					<view class="action-btn complete-btn" @click.stop="completeAppointment(appointment)" v-if="appointment.status === '1'">
						<uni-icons type="flag" size="16" color="#007AFF"></uni-icons>
						<text class="action-text">完成</text>
					</view>
					<view class="action-btn cancel-btn" @click.stop="showCancelDialog(appointment)" v-if="appointment.status === '0' || appointment.status === '1'">
						<uni-icons type="closeempty" size="16" color="#FF3B30"></uni-icons>
						<text class="action-text">取消</text>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-tip" v-if="filteredAppointments.length === 0">
				<image class="empty-icon" src="/static/images/empty.png"></image>
				<text class="empty-text">暂无预约订单</text>
			</view>
		</view>
		
		<!-- 日历选择器 -->
		<uni-calendar 
			:insert="false"
			:start-date="startDate"
			:end-date="endDate"
			:range="true"
			:clearDate="false"
			v-model="showCalendar"
			@confirm="dateConfirm"
		/>
		
		<!-- 取消预约弹窗 -->
		<uni-popup ref="cancelPopup" type="dialog">
			<uni-popup-dialog
				title="取消预约"
				:content="cancelDialog.content"
				:type="cancelDialog.type"
				:before-close="true"
				confirmText="确认取消"
				cancelText="关闭"
				@confirm="submitCancelAppointment"
				@close="closeCancelDialog"
			>
				<uni-easyinput
					type="textarea"
					v-model="cancelDialog.reason"
					placeholder="请输入取消原因（必填）"
					:maxlength="100"
					style="margin-top: 10px;"
				/>
			</uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { getBookingList, updateBookingStatus, cancelBooking } from '../../api/merchant/booking.js';

// 初始化日期
const today = new Date();
const year = today.getFullYear();
const month = today.getMonth();
const day = today.getDate();

// 响应式数据
const activeFilter = ref('all');
const searchKey = ref('');
const dateRange = ref([]); // 日期范围
const startDate = ref(''); // 日历开始日期
const endDate = ref(''); // 日历结束日期
const showCalendar = ref(false);
const dateText = ref('全部日期');
const cancelPopup = ref(null);

const appointments = ref([
	{
		id: 1,
		orderNo: 'AP2023090101',
		userName: '张先生',
		userPhone: '13800138000',
		type: 'coach', // coach 教练预约，table 台球桌预约
		coachName: '李教练',
		coachId: 1,
		appointmentDate: '2023-09-01',
		startTime: '10:00',
		endTime: '12:00',
		duration: 120, // 分钟
		totalAmount: '200.00',
		status: '0', // 0 待确认，1 已确认，2 已完成，3 已取消
		createTime: '2023-09-01 08:30'
	},
	{
		id: 2,
		orderNo: 'AP2023090102',
		userName: '王女士',
		userPhone: '13800138001',
		type: 'table',
		tableName: '1号台',
		tableId: 1,
		appointmentDate: '2023-09-01',
		startTime: '14:00',
		endTime: '16:00',
		duration: 120,
		totalAmount: '60.00',
		status: '1',
		createTime: '2023-09-01 09:15'
	},
	{
		id: 3,
		orderNo: 'AP2023090103',
		userName: '李先生',
		userPhone: '13800138002',
		type: 'coach',
		coachName: '张教练',
		coachId: 2,
		appointmentDate: '2023-09-02',
		startTime: '09:00',
		endTime: '11:00',
		duration: 120,
		totalAmount: '240.00',
		status: '2',
		createTime: '2023-09-01 16:45'
	},
	{
		id: 4,
		orderNo: 'AP2023090104',
		userName: '赵女士',
		userPhone: '13800138003',
		type: 'table',
		tableName: '3号台',
		tableId: 3,
		appointmentDate: '2023-09-02',
		startTime: '19:00',
		endTime: '21:00',
		duration: 120,
		totalAmount: '80.00',
		status: '3',
		createTime: '2023-09-01 18:20',
		cancelReason: '用户临时有事取消'
	}
]);

const cancelDialog = ref({
	appointment: null,
	reason: '',
	content: '',
	type: 'info'
});

// 计算属性
const filteredAppointments = computed(() => {
	let result = [...appointments.value];
	
	// 按搜索关键词过滤
	if (searchKey.value) {
		result = result.filter(appointment => 
			appointment.orderNo.includes(searchKey.value) || 
			appointment.userName.includes(searchKey.value)
		);
	}
	
	// 按状态过滤
	if (activeFilter.value === 'pending') {
		result = result.filter(appointment => appointment.status === '0');
	} else if (activeFilter.value === 'confirmed') {
		result = result.filter(appointment => appointment.status === '1');
	} else if (activeFilter.value === 'completed') {
		result = result.filter(appointment => appointment.status === '2');
	} else if (activeFilter.value === 'canceled') {
		result = result.filter(appointment => appointment.status === '3');
	}
	
	// 按日期范围过滤
	if (dateRange.value && dateRange.value.length === 2) {
		const startDate = dateRange.value[0];
		const endDate = dateRange.value[1];
		result = result.filter(appointment => {
			const appointmentDate = new Date(appointment.appointmentDate);
			return appointmentDate >= new Date(startDate) && appointmentDate <= new Date(endDate);
		});
	}
	
	return result;
});

// 页面加载时
onMounted(() => {
	loadAppointments();
});

// 加载预约数据
const loadAppointments = async () => {
	try {
		uni.showLoading({
			title: '加载中...'
		});

		const response = await getBookingList({
			page: 1,
			pageSize: 100,
			search: searchKey.value,
			status: activeFilter.value === 'all' ? '' : getStatusValueByFilter(activeFilter.value),
			start_date: dateRange.value.length === 2 ? dateRange.value[0] : '',
			end_date: dateRange.value.length === 2 ? dateRange.value[1] : ''
		});

		uni.hideLoading();

		if (response.code === 200) {
			appointments.value = response.data.list.map(booking => ({
				id: booking.id,
				orderNo: booking.order_no,
				userName: booking.customer_name,
				userPhone: booking.customer_phone,
				type: booking.type, // coach 或 table
				coachName: booking.coach_name,
				coachId: booking.coach_id,
				tableName: booking.table_name,
				tableId: booking.table_id,
				appointmentDate: booking.appointment_date,
				startTime: booking.start_time,
				endTime: booking.end_time,
				duration: booking.duration,
				totalAmount: booking.total_amount,
				status: booking.status,
				createTime: booking.created_at,
				cancelReason: booking.cancel_reason || ''
			}));
		} else {
			uni.showToast({
				title: response.msg || '获取预约列表失败',
				icon: 'none'
			});
		}
	} catch (error) {
		uni.hideLoading();
		console.error('获取预约列表失败', error);
		uni.showToast({
			title: '网络错误，请稍后重试',
			icon: 'none'
		});
	}
};

// 根据过滤器获取状态值
const getStatusValueByFilter = (filter) => {
	const filterMap = {
		'pending': '0',
		'confirmed': '1',
		'completed': '2',
		'canceled': '3'
	};
	return filterMap[filter] || '';
};

// 过滤预约
const filterAppointments = (type) => {
	activeFilter.value = type;
	// 重新加载数据
	loadAppointments();
};

// 搜索预约
const searchAppointments = () => {
	// 重新加载数据
	loadAppointments();
};

// 日期确认
const dateConfirm = (range) => {
	if (range && range.length === 2) {
		dateRange.value = range;
		dateText.value = `${range[0]} - ${range[1]}`;
	} else {
		dateRange.value = [];
		dateText.value = '全部日期';
	}
	showCalendar.value = false;
	// 重新加载数据
	loadAppointments();
};

// 导航到指定页面
const navigateTo = (url) => {
	uni.navigateTo({
		url: url
	});
};

// 联系用户
const callUser = (appointment) => {
	uni.makePhoneCall({
		phoneNumber: appointment.userPhone,
		success: () => {
			console.log('拨打电话成功');
		},
		fail: (err) => {
			console.log('拨打电话失败', err);
		}
	});
};

// 确认预约
const confirmAppointment = async (appointment) => {
	uni.showModal({
		title: '确认预约',
		content: `确定确认【${appointment.orderNo}】的预约吗？`,
		success: async (res) => {
			if (res.confirm) {
				try {
					uni.showLoading({
						title: '处理中...'
					});

					const response = await updateBookingStatus({
						booking_id: appointment.id,
						status: '1'
					});

					uni.hideLoading();

					if (response.code === 200) {
						// 更新本地数据
						const index = appointments.value.findIndex(a => a.id === appointment.id);
						if (index !== -1) {
							appointments.value[index].status = '1';
						}
						
						uni.showToast({
							title: '预约已确认',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: response.msg || '确认失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('确认预约失败', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				}
			}
		}
	});
};

// 完成预约
const completeAppointment = async (appointment) => {
	uni.showModal({
		title: '完成预约',
		content: `确定将【${appointment.orderNo}】标记为已完成吗？`,
		success: async (res) => {
			if (res.confirm) {
				try {
					uni.showLoading({
						title: '处理中...'
					});

					const response = await updateBookingStatus({
						booking_id: appointment.id,
						status: '2'
					});

					uni.hideLoading();

					if (response.code === 200) {
						// 更新本地数据
						const index = appointments.value.findIndex(a => a.id === appointment.id);
						if (index !== -1) {
							appointments.value[index].status = '2';
						}
						
						uni.showToast({
							title: '预约已完成',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: response.msg || '操作失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('完成预约失败', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				}
			}
		}
	});
};

// 显示取消对话框
const showCancelDialog = (appointment) => {
	cancelDialog.value.appointment = appointment;
	cancelDialog.value.content = `确定要取消【${appointment.orderNo}】的预约吗？`;
	cancelDialog.value.reason = '';
	cancelPopup.value.open();
};

// 提交取消预约
const submitCancelAppointment = async () => {
	if (!cancelDialog.value.reason.trim()) {
		uni.showToast({
			title: '请输入取消原因',
			icon: 'none'
		});
		return;
	}
	
	try {
		uni.showLoading({
			title: '处理中...'
		});

		const response = await cancelBooking({
			booking_id: cancelDialog.value.appointment.id,
			reason: cancelDialog.value.reason.trim()
		});

		uni.hideLoading();

		if (response.code === 200) {
			// 更新本地数据
			const appointment = cancelDialog.value.appointment;
			const index = appointments.value.findIndex(a => a.id === appointment.id);
			if (index !== -1) {
				appointments.value[index].status = '3';
				appointments.value[index].cancelReason = cancelDialog.value.reason.trim();
			}
			
			uni.showToast({
				title: '预约已取消',
				icon: 'success'
			});
		} else {
			uni.showToast({
				title: response.msg || '取消失败',
				icon: 'none'
			});
		}
	} catch (error) {
		uni.hideLoading();
		console.error('取消预约失败', error);
		uni.showToast({
			title: '网络错误，请稍后重试',
			icon: 'none'
		});
	}
	
	closeCancelDialog();
};

// 关闭取消对话框
const closeCancelDialog = () => {
	cancelPopup.value.close();
};

// 获取状态文本
const getStatusText = (status) => {
	const statusMap = {
		'0': '待确认',
		'1': '已确认',
		'2': '已完成',
		'3': '已取消'
	};
	return statusMap[status] || '未知';
};

// 获取总数量
const getTotalCount = () => {
	return appointments.value.length;
};

// 按状态获取数量
const getCountByStatus = (status) => {
	return appointments.value.filter(appointment => appointment.status === status).length;
};
</script>

<style lang="scss">
	.appointment-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 30rpx;
	}
	
	.status-filter {
		display: flex;
		background-color: #FFFFFF;
		padding: 0 20rpx;
		overflow-x: auto;
		white-space: nowrap;
	}
	
	.filter-item {
		padding: 20rpx 30rpx;
		font-size: 28rpx;
		color: #666666;
		position: relative;
		flex-shrink: 0;
	}
	
	.filter-item.active {
		color: #007AFF;
		font-weight: bold;
		
		&::after {
			content: '';
			position: absolute;
			left: 30rpx;
			right: 30rpx;
			bottom: 0;
			height: 4rpx;
			background-color: #007AFF;
			border-radius: 2rpx;
		}
	}
	
	.search-bar {
		padding: 20rpx;
		background-color: #FFFFFF;
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		border-bottom: 1rpx solid #EEEEEE;
	}
	
	.search-input-box {
		flex: 1;
		height: 72rpx;
		background-color: #F5F5F5;
		border-radius: 36rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		margin-right: 20rpx;
	}
	
	.search-input {
		flex: 1;
		height: 72rpx;
		font-size: 28rpx;
		margin-left: 10rpx;
	}
	
	.date-filter {
		height: 72rpx;
		display: flex;
		align-items: center;
		background-color: #F0F7FF;
		padding: 0 20rpx;
		border-radius: 36rpx;
	}
	
	.date-text {
		font-size: 24rpx;
		color: #007AFF;
		margin-right: 10rpx;
		max-width: 200rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	
	.appointment-list {
		padding: 0 20rpx;
	}
	
	.appointment-item {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.appointment-header {
		display: flex;
		padding: 20rpx;
		border-bottom: 1rpx solid #F5F5F5;
		align-items: center;
	}
	
	.appointment-type {
		font-size: 24rpx;
		padding: 4rpx 12rpx;
		border-radius: 6rpx;
		margin-right: 20rpx;
	}
	
	.type-coach {
		background-color: #EBFFEC;
		color: #00CC66;
	}
	
	.type-table {
		background-color: #E3F8FF;
		color: #0091FF;
	}
	
	.appointment-id {
		flex: 1;
		font-size: 26rpx;
		color: #666666;
	}
	
	.appointment-status {
		font-size: 24rpx;
		padding: 4rpx 12rpx;
		border-radius: 6rpx;
	}
	
	.status-0 {
		background-color: #FFF3E0;
		color: #FF9500;
	}
	
	.status-1 {
		background-color: #E3F8FF;
		color: #0091FF;
	}
	
	.status-2 {
		background-color: #EBFFEC;
		color: #00CC66;
	}
	
	.status-3 {
		background-color: #F5F5F5;
		color: #999999;
	}
	
	.appointment-content {
		padding: 20rpx;
	}
	
	.info-item {
		display: flex;
		margin-bottom: 10rpx;
		font-size: 28rpx;
	}
	
	.label {
		width: 120rpx;
		color: #999999;
	}
	
	.value {
		flex: 1;
		color: #333333;
	}
	
	.price {
		color: #FF3B30;
		font-weight: bold;
	}
	
	.appointment-actions {
		display: flex;
		padding: 20rpx;
		border-top: 1rpx solid #F5F5F5;
	}
	
	.action-btn {
		display: flex;
		align-items: center;
		padding: 10rpx 20rpx;
		margin-right: 20rpx;
		border-radius: 30rpx;
		background-color: #F5F5F5;
	}
	
	.confirm-btn {
		background-color: rgba(0, 204, 102, 0.1);
	}
	
	.complete-btn {
		background-color: rgba(0, 122, 255, 0.1);
	}
	
	.cancel-btn {
		background-color: rgba(255, 59, 48, 0.1);
	}
	
	.action-text {
		font-size: 24rpx;
		color: #666666;
		margin-left: 6rpx;
	}
	
	.confirm-btn .action-text {
		color: #00CC66;
	}
	
	.complete-btn .action-text {
		color: #007AFF;
	}
	
	.cancel-btn .action-text {
		color: #FF3B30;
	}
	
	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 100rpx 0;
	}
	
	.empty-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
</style> 