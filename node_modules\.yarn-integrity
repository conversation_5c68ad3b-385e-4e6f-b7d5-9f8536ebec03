{"systemParams": "win32-x64-108", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@dcloudio/types@^3.4.8", "@dcloudio/uni-app@^3.0.0-4020920240930001", "@dcloudio/uni-cli-shared@^3.0.0-4020920240930001", "@dcloudio/uni-components@^3.0.0-4020920240930001", "@dcloudio/uni-h5@^3.0.0-4020920240930001", "@dcloudio/uni-mp-weixin@^3.0.0-4020920240930001", "@dcloudio/uni-ui@^1.5.7", "@dcloudio/vite-plugin-uni@^3.0.0-4020920240930001", "@vitejs/plugin-vue@^5.2.4", "sass@^1.89.0", "vite@^4.5.0", "vue@^3.3.8"], "lockfileEntries": {"@ampproject/remapping@^2.1.2": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "@ampproject/remapping@^2.2.0": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "@babel/code-frame@^7.23.5": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/code-frame@^7.27.1": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/compat-data@^7.27.2": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.5.tgz", "@babel/core@^7.23.3": "https://registry.npmjs.org/@babel/core/-/core-7.27.4.tgz", "@babel/generator@^7.20.5": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.5.tgz", "@babel/generator@^7.27.3": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.5.tgz", "@babel/helper-compilation-targets@^7.27.2": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "@babel/helper-module-imports@^7.27.1": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "@babel/helper-module-transforms@^7.27.3": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "@babel/helper-string-parser@^7.27.1": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "@babel/helper-validator-identifier@^7.27.1": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "@babel/helper-validator-option@^7.27.1": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "@babel/helpers@^7.27.4": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz", "@babel/parser@^7.23.9": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "@babel/parser@^7.27.2": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "@babel/parser@^7.27.4": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "@babel/parser@^7.27.5": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "@babel/runtime@^7.7.2": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/template@^7.27.2": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "@babel/traverse@^7.27.1": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.4.tgz", "@babel/traverse@^7.27.3": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.4.tgz", "@babel/traverse@^7.27.4": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.4.tgz", "@babel/types@^7.20.7": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "@babel/types@^7.27.1": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "@babel/types@^7.27.3": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "@babel/types@^7.27.6": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "@dcloudio/types@^3.4.8": "https://registry.npmjs.org/@dcloudio/types/-/types-3.4.15.tgz", "@dcloudio/uni-app@^3.0.0-4020920240930001": "https://registry.npmjs.org/@dcloudio/uni-app/-/uni-app-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-cli-shared@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-cli-shared/-/uni-cli-shared-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-cli-shared@^3.0.0-4020920240930001": "https://registry.npmjs.org/@dcloudio/uni-cli-shared/-/uni-cli-shared-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-cloud@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-cloud/-/uni-cloud-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-components@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-components/-/uni-components-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-components@^3.0.0-4020920240930001": "https://registry.npmjs.org/@dcloudio/uni-components/-/uni-components-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-console@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-console/-/uni-console-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-h5-vite@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-h5-vite/-/uni-h5-vite-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-h5-vue@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-h5-vue/-/uni-h5-vue-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-h5@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-h5/-/uni-h5-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-h5@^3.0.0-4020920240930001": "https://registry.npmjs.org/@dcloudio/uni-h5/-/uni-h5-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-i18n@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-i18n/-/uni-i18n-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-mp-compiler@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-mp-compiler/-/uni-mp-compiler-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-mp-vite@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-mp-vite/-/uni-mp-vite-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-mp-vue@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-mp-vue/-/uni-mp-vue-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-mp-weixin@^3.0.0-4020920240930001": "https://registry.npmjs.org/@dcloudio/uni-mp-weixin/-/uni-mp-weixin-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-push@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-push/-/uni-push-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-shared@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-shared/-/uni-shared-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-stat@3.0.0-alpha-4070220250613001": "https://registry.npmjs.org/@dcloudio/uni-stat/-/uni-stat-3.0.0-alpha-4070220250613001.tgz", "@dcloudio/uni-ui@^1.5.7": "https://registry.npmjs.org/@dcloudio/uni-ui/-/uni-ui-1.5.7.tgz", "@dcloudio/vite-plugin-uni@^3.0.0-4020920240930001": "https://registry.npmjs.org/@dcloudio/vite-plugin-uni/-/vite-plugin-uni-3.0.0-alpha-3000020210521001.tgz", "@esbuild/aix-ppc64@0.20.2": "https://registry.yarnpkg.com/@esbuild/aix-ppc64/-/aix-ppc64-0.20.2.tgz#a70f4ac11c6a1dfc18b8bbb13284155d933b9537", "@esbuild/android-arm64@0.18.20": "https://registry.yarnpkg.com/@esbuild/android-arm64/-/android-arm64-0.18.20.tgz#984b4f9c8d0377443cc2dfcef266d02244593622", "@esbuild/android-arm64@0.20.2": "https://registry.yarnpkg.com/@esbuild/android-arm64/-/android-arm64-0.20.2.tgz#db1c9202a5bc92ea04c7b6840f1bbe09ebf9e6b9", "@esbuild/android-arm@0.18.20": "https://registry.yarnpkg.com/@esbuild/android-arm/-/android-arm-0.18.20.tgz#fedb265bc3a589c84cc11f810804f234947c3682", "@esbuild/android-arm@0.20.2": "https://registry.yarnpkg.com/@esbuild/android-arm/-/android-arm-0.20.2.tgz#3b488c49aee9d491c2c8f98a909b785870d6e995", "@esbuild/android-x64@0.18.20": "https://registry.yarnpkg.com/@esbuild/android-x64/-/android-x64-0.18.20.tgz#35cf419c4cfc8babe8893d296cd990e9e9f756f2", "@esbuild/android-x64@0.20.2": "https://registry.yarnpkg.com/@esbuild/android-x64/-/android-x64-0.20.2.tgz#3b1628029e5576249d2b2d766696e50768449f98", "@esbuild/darwin-arm64@0.18.20": "https://registry.yarnpkg.com/@esbuild/darwin-arm64/-/darwin-arm64-0.18.20.tgz#08172cbeccf95fbc383399a7f39cfbddaeb0d7c1", "@esbuild/darwin-arm64@0.20.2": "https://registry.yarnpkg.com/@esbuild/darwin-arm64/-/darwin-arm64-0.20.2.tgz#6e8517a045ddd86ae30c6608c8475ebc0c4000bb", "@esbuild/darwin-x64@0.18.20": "https://registry.yarnpkg.com/@esbuild/darwin-x64/-/darwin-x64-0.18.20.tgz#d70d5790d8bf475556b67d0f8b7c5bdff053d85d", "@esbuild/darwin-x64@0.20.2": "https://registry.yarnpkg.com/@esbuild/darwin-x64/-/darwin-x64-0.20.2.tgz#90ed098e1f9dd8a9381695b207e1cff45540a0d0", "@esbuild/freebsd-arm64@0.18.20": "https://registry.yarnpkg.com/@esbuild/freebsd-arm64/-/freebsd-arm64-0.18.20.tgz#98755cd12707f93f210e2494d6a4b51b96977f54", "@esbuild/freebsd-arm64@0.20.2": "https://registry.yarnpkg.com/@esbuild/freebsd-arm64/-/freebsd-arm64-0.20.2.tgz#d71502d1ee89a1130327e890364666c760a2a911", "@esbuild/freebsd-x64@0.18.20": "https://registry.yarnpkg.com/@esbuild/freebsd-x64/-/freebsd-x64-0.18.20.tgz#c1eb2bff03915f87c29cece4c1a7fa1f423b066e", "@esbuild/freebsd-x64@0.20.2": "https://registry.yarnpkg.com/@esbuild/freebsd-x64/-/freebsd-x64-0.20.2.tgz#aa5ea58d9c1dd9af688b8b6f63ef0d3d60cea53c", "@esbuild/linux-arm64@0.18.20": "https://registry.yarnpkg.com/@esbuild/linux-arm64/-/linux-arm64-0.18.20.tgz#bad4238bd8f4fc25b5a021280c770ab5fc3a02a0", "@esbuild/linux-arm64@0.20.2": "https://registry.yarnpkg.com/@esbuild/linux-arm64/-/linux-arm64-0.20.2.tgz#055b63725df678379b0f6db9d0fa85463755b2e5", "@esbuild/linux-arm@0.18.20": "https://registry.yarnpkg.com/@esbuild/linux-arm/-/linux-arm-0.18.20.tgz#3e617c61f33508a27150ee417543c8ab5acc73b0", "@esbuild/linux-arm@0.20.2": "https://registry.yarnpkg.com/@esbuild/linux-arm/-/linux-arm-0.20.2.tgz#76b3b98cb1f87936fbc37f073efabad49dcd889c", "@esbuild/linux-ia32@0.18.20": "https://registry.yarnpkg.com/@esbuild/linux-ia32/-/linux-ia32-0.18.20.tgz#699391cccba9aee6019b7f9892eb99219f1570a7", "@esbuild/linux-ia32@0.20.2": "https://registry.yarnpkg.com/@esbuild/linux-ia32/-/linux-ia32-0.20.2.tgz#c0e5e787c285264e5dfc7a79f04b8b4eefdad7fa", "@esbuild/linux-loong64@0.18.20": "https://registry.yarnpkg.com/@esbuild/linux-loong64/-/linux-loong64-0.18.20.tgz#e6fccb7aac178dd2ffb9860465ac89d7f23b977d", "@esbuild/linux-loong64@0.20.2": "https://registry.yarnpkg.com/@esbuild/linux-loong64/-/linux-loong64-0.20.2.tgz#a6184e62bd7cdc63e0c0448b83801001653219c5", "@esbuild/linux-mips64el@0.18.20": "https://registry.yarnpkg.com/@esbuild/linux-mips64el/-/linux-mips64el-0.18.20.tgz#eeff3a937de9c2310de30622a957ad1bd9183231", "@esbuild/linux-mips64el@0.20.2": "https://registry.yarnpkg.com/@esbuild/linux-mips64el/-/linux-mips64el-0.20.2.tgz#d08e39ce86f45ef8fc88549d29c62b8acf5649aa", "@esbuild/linux-ppc64@0.18.20": "https://registry.yarnpkg.com/@esbuild/linux-ppc64/-/linux-ppc64-0.18.20.tgz#2f7156bde20b01527993e6881435ad79ba9599fb", "@esbuild/linux-ppc64@0.20.2": "https://registry.yarnpkg.com/@esbuild/linux-ppc64/-/linux-ppc64-0.20.2.tgz#8d252f0b7756ffd6d1cbde5ea67ff8fd20437f20", "@esbuild/linux-riscv64@0.18.20": "https://registry.yarnpkg.com/@esbuild/linux-riscv64/-/linux-riscv64-0.18.20.tgz#6628389f210123d8b4743045af8caa7d4ddfc7a6", "@esbuild/linux-riscv64@0.20.2": "https://registry.yarnpkg.com/@esbuild/linux-riscv64/-/linux-riscv64-0.20.2.tgz#19f6dcdb14409dae607f66ca1181dd4e9db81300", "@esbuild/linux-s390x@0.18.20": "https://registry.yarnpkg.com/@esbuild/linux-s390x/-/linux-s390x-0.18.20.tgz#255e81fb289b101026131858ab99fba63dcf0071", "@esbuild/linux-s390x@0.20.2": "https://registry.yarnpkg.com/@esbuild/linux-s390x/-/linux-s390x-0.20.2.tgz#3c830c90f1a5d7dd1473d5595ea4ebb920988685", "@esbuild/linux-x64@0.18.20": "https://registry.yarnpkg.com/@esbuild/linux-x64/-/linux-x64-0.18.20.tgz#c7690b3417af318a9b6f96df3031a8865176d338", "@esbuild/linux-x64@0.20.2": "https://registry.yarnpkg.com/@esbuild/linux-x64/-/linux-x64-0.20.2.tgz#86eca35203afc0d9de0694c64ec0ab0a378f6fff", "@esbuild/netbsd-x64@0.18.20": "https://registry.yarnpkg.com/@esbuild/netbsd-x64/-/netbsd-x64-0.18.20.tgz#30e8cd8a3dded63975e2df2438ca109601ebe0d1", "@esbuild/netbsd-x64@0.20.2": "https://registry.yarnpkg.com/@esbuild/netbsd-x64/-/netbsd-x64-0.20.2.tgz#e771c8eb0e0f6e1877ffd4220036b98aed5915e6", "@esbuild/openbsd-x64@0.18.20": "https://registry.yarnpkg.com/@esbuild/openbsd-x64/-/openbsd-x64-0.18.20.tgz#7812af31b205055874c8082ea9cf9ab0da6217ae", "@esbuild/openbsd-x64@0.20.2": "https://registry.yarnpkg.com/@esbuild/openbsd-x64/-/openbsd-x64-0.20.2.tgz#9a795ae4b4e37e674f0f4d716f3e226dd7c39baf", "@esbuild/sunos-x64@0.18.20": "https://registry.yarnpkg.com/@esbuild/sunos-x64/-/sunos-x64-0.18.20.tgz#d5c275c3b4e73c9b0ecd38d1ca62c020f887ab9d", "@esbuild/sunos-x64@0.20.2": "https://registry.yarnpkg.com/@esbuild/sunos-x64/-/sunos-x64-0.20.2.tgz#7df23b61a497b8ac189def6e25a95673caedb03f", "@esbuild/win32-arm64@0.18.20": "https://registry.yarnpkg.com/@esbuild/win32-arm64/-/win32-arm64-0.18.20.tgz#73bc7f5a9f8a77805f357fab97f290d0e4820ac9", "@esbuild/win32-arm64@0.20.2": "https://registry.yarnpkg.com/@esbuild/win32-arm64/-/win32-arm64-0.20.2.tgz#f1ae5abf9ca052ae11c1bc806fb4c0f519bacf90", "@esbuild/win32-ia32@0.18.20": "https://registry.yarnpkg.com/@esbuild/win32-ia32/-/win32-ia32-0.18.20.tgz#ec93cbf0ef1085cc12e71e0d661d20569ff42102", "@esbuild/win32-ia32@0.20.2": "https://registry.yarnpkg.com/@esbuild/win32-ia32/-/win32-ia32-0.20.2.tgz#241fe62c34d8e8461cd708277813e1d0ba55ce23", "@esbuild/win32-x64@0.18.20": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.18.20.tgz", "@esbuild/win32-x64@0.20.2": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.20.2.tgz", "@intlify/core-base@9.1.9": "https://registry.npmjs.org/@intlify/core-base/-/core-base-9.1.9.tgz", "@intlify/devtools-if@9.1.9": "https://registry.npmjs.org/@intlify/devtools-if/-/devtools-if-9.1.9.tgz", "@intlify/message-compiler@9.1.9": "https://registry.npmjs.org/@intlify/message-compiler/-/message-compiler-9.1.9.tgz", "@intlify/message-resolver@9.1.9": "https://registry.npmjs.org/@intlify/message-resolver/-/message-resolver-9.1.9.tgz", "@intlify/runtime@9.1.9": "https://registry.npmjs.org/@intlify/runtime/-/runtime-9.1.9.tgz", "@intlify/shared@9.1.9": "https://registry.npmjs.org/@intlify/shared/-/shared-9.1.9.tgz", "@intlify/vue-devtools@9.1.9": "https://registry.npmjs.org/@intlify/vue-devtools/-/vue-devtools-9.1.9.tgz", "@jimp/bmp@^0.10.3": "https://registry.npmjs.org/@jimp/bmp/-/bmp-0.10.3.tgz", "@jimp/core@^0.10.3": "https://registry.npmjs.org/@jimp/core/-/core-0.10.3.tgz", "@jimp/custom@^0.10.3": "https://registry.npmjs.org/@jimp/custom/-/custom-0.10.3.tgz", "@jimp/gif@^0.10.3": "https://registry.npmjs.org/@jimp/gif/-/gif-0.10.3.tgz", "@jimp/jpeg@^0.10.3": "https://registry.npmjs.org/@jimp/jpeg/-/jpeg-0.10.3.tgz", "@jimp/plugin-blit@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-blit/-/plugin-blit-0.10.3.tgz", "@jimp/plugin-blur@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-blur/-/plugin-blur-0.10.3.tgz", "@jimp/plugin-circle@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-circle/-/plugin-circle-0.10.3.tgz", "@jimp/plugin-color@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-color/-/plugin-color-0.10.3.tgz", "@jimp/plugin-contain@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-contain/-/plugin-contain-0.10.3.tgz", "@jimp/plugin-cover@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-cover/-/plugin-cover-0.10.3.tgz", "@jimp/plugin-crop@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-crop/-/plugin-crop-0.10.3.tgz", "@jimp/plugin-displace@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-displace/-/plugin-displace-0.10.3.tgz", "@jimp/plugin-dither@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-dither/-/plugin-dither-0.10.3.tgz", "@jimp/plugin-fisheye@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-fisheye/-/plugin-fisheye-0.10.3.tgz", "@jimp/plugin-flip@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-flip/-/plugin-flip-0.10.3.tgz", "@jimp/plugin-gaussian@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-gaussian/-/plugin-gaussian-0.10.3.tgz", "@jimp/plugin-invert@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-invert/-/plugin-invert-0.10.3.tgz", "@jimp/plugin-mask@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-mask/-/plugin-mask-0.10.3.tgz", "@jimp/plugin-normalize@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-normalize/-/plugin-normalize-0.10.3.tgz", "@jimp/plugin-print@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-print/-/plugin-print-0.10.3.tgz", "@jimp/plugin-resize@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-resize/-/plugin-resize-0.10.3.tgz", "@jimp/plugin-rotate@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-rotate/-/plugin-rotate-0.10.3.tgz", "@jimp/plugin-scale@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-scale/-/plugin-scale-0.10.3.tgz", "@jimp/plugin-shadow@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-shadow/-/plugin-shadow-0.10.3.tgz", "@jimp/plugin-threshold@^0.10.3": "https://registry.npmjs.org/@jimp/plugin-threshold/-/plugin-threshold-0.10.3.tgz", "@jimp/plugins@^0.10.3": "https://registry.npmjs.org/@jimp/plugins/-/plugins-0.10.3.tgz", "@jimp/png@^0.10.3": "https://registry.npmjs.org/@jimp/png/-/png-0.10.3.tgz", "@jimp/tiff@^0.10.3": "https://registry.npmjs.org/@jimp/tiff/-/tiff-0.10.3.tgz", "@jimp/types@^0.10.3": "https://registry.npmjs.org/@jimp/types/-/types-0.10.3.tgz", "@jimp/utils@^0.10.3": "https://registry.npmjs.org/@jimp/utils/-/utils-0.10.3.tgz", "@jridgewell/gen-mapping@^0.3.5": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "@jridgewell/resolve-uri@^3.1.0": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "@jridgewell/set-array@^1.2.1": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz", "@jridgewell/sourcemap-codec@^1.4.10": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "@jridgewell/sourcemap-codec@^1.4.14": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "@jridgewell/sourcemap-codec@^1.5.0": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "@jridgewell/trace-mapping@^0.3.24": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "@jridgewell/trace-mapping@^0.3.25": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "@nodelib/fs.scandir@2.1.5": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "@nodelib/fs.stat@2.0.5": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.stat@^2.0.2": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "@nodelib/fs.walk@^1.2.3": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "@parcel/watcher-android-arm64@2.5.1": "https://registry.yarnpkg.com/@parcel/watcher-android-arm64/-/watcher-android-arm64-2.5.1.tgz#507f836d7e2042f798c7d07ad19c3546f9848ac1", "@parcel/watcher-darwin-arm64@2.5.1": "https://registry.yarnpkg.com/@parcel/watcher-darwin-arm64/-/watcher-darwin-arm64-2.5.1.tgz#3d26dce38de6590ef79c47ec2c55793c06ad4f67", "@parcel/watcher-darwin-x64@2.5.1": "https://registry.yarnpkg.com/@parcel/watcher-darwin-x64/-/watcher-darwin-x64-2.5.1.tgz#99f3af3869069ccf774e4ddfccf7e64fd2311ef8", "@parcel/watcher-freebsd-x64@2.5.1": "https://registry.yarnpkg.com/@parcel/watcher-freebsd-x64/-/watcher-freebsd-x64-2.5.1.tgz#14d6857741a9f51dfe51d5b08b7c8afdbc73ad9b", "@parcel/watcher-linux-arm-glibc@2.5.1": "https://registry.yarnpkg.com/@parcel/watcher-linux-arm-glibc/-/watcher-linux-arm-glibc-2.5.1.tgz#43c3246d6892381db473bb4f663229ad20b609a1", "@parcel/watcher-linux-arm-musl@2.5.1": "https://registry.yarnpkg.com/@parcel/watcher-linux-arm-musl/-/watcher-linux-arm-musl-2.5.1.tgz#663750f7090bb6278d2210de643eb8a3f780d08e", "@parcel/watcher-linux-arm64-glibc@2.5.1": "https://registry.yarnpkg.com/@parcel/watcher-linux-arm64-glibc/-/watcher-linux-arm64-glibc-2.5.1.tgz#ba60e1f56977f7e47cd7e31ad65d15fdcbd07e30", "@parcel/watcher-linux-arm64-musl@2.5.1": "https://registry.yarnpkg.com/@parcel/watcher-linux-arm64-musl/-/watcher-linux-arm64-musl-2.5.1.tgz#f7fbcdff2f04c526f96eac01f97419a6a99855d2", "@parcel/watcher-linux-x64-glibc@2.5.1": "https://registry.yarnpkg.com/@parcel/watcher-linux-x64-glibc/-/watcher-linux-x64-glibc-2.5.1.tgz#4d2ea0f633eb1917d83d483392ce6181b6a92e4e", "@parcel/watcher-linux-x64-musl@2.5.1": "https://registry.yarnpkg.com/@parcel/watcher-linux-x64-musl/-/watcher-linux-x64-musl-2.5.1.tgz#277b346b05db54f55657301dd77bdf99d63606ee", "@parcel/watcher-win32-arm64@2.5.1": "https://registry.yarnpkg.com/@parcel/watcher-win32-arm64/-/watcher-win32-arm64-2.5.1.tgz#7e9e02a26784d47503de1d10e8eab6cceb524243", "@parcel/watcher-win32-ia32@2.5.1": "https://registry.yarnpkg.com/@parcel/watcher-win32-ia32/-/watcher-win32-ia32-2.5.1.tgz#2d0f94fa59a873cdc584bf7f6b1dc628ddf976e6", "@parcel/watcher-win32-x64@2.5.1": "https://registry.npmjs.org/@parcel/watcher-win32-x64/-/watcher-win32-x64-2.5.1.tgz", "@parcel/watcher@^2.4.1": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.5.1.tgz", "@rollup/pluginutils@^4.1.0": "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-4.2.1.tgz", "@rollup/pluginutils@^5.0.5": "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.1.4.tgz", "@types/estree@^1.0.0": "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz", "@types/fs-extra@^8.0.1": "https://registry.npmjs.org/@types/fs-extra/-/fs-extra-8.1.5.tgz", "@types/glob@^7.1.1": "https://registry.npmjs.org/@types/glob/-/glob-7.2.0.tgz", "@types/minimatch@*": "https://registry.npmjs.org/@types/minimatch/-/minimatch-5.1.2.tgz", "@types/node@*": "https://registry.npmjs.org/@types/node/-/node-24.0.1.tgz", "@vitejs/plugin-vue@^5.2.4": "https://registry.yarnpkg.com/@vitejs/plugin-vue/-/plugin-vue-5.2.4.tgz#9e8a512eb174bfc2a333ba959bbf9de428d89ad8", "@vue/compiler-core@3.4.21": "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.4.21.tgz", "@vue/compiler-core@3.5.16": "https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.5.16.tgz", "@vue/compiler-dom@3.4.21": "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.4.21.tgz", "@vue/compiler-dom@3.5.16": "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.5.16.tgz", "@vue/compiler-sfc@3.4.21": "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.4.21.tgz", "@vue/compiler-sfc@3.5.16": "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.5.16.tgz", "@vue/compiler-ssr@3.4.21": "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.4.21.tgz", "@vue/compiler-ssr@3.5.16": "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.5.16.tgz", "@vue/devtools-api@^6.6.4": "https://registry.npmjs.org/@vue/devtools-api/-/devtools-api-6.6.4.tgz", "@vue/reactivity@3.5.16": "https://registry.npmjs.org/@vue/reactivity/-/reactivity-3.5.16.tgz", "@vue/runtime-core@3.5.16": "https://registry.npmjs.org/@vue/runtime-core/-/runtime-core-3.5.16.tgz", "@vue/runtime-dom@3.5.16": "https://registry.npmjs.org/@vue/runtime-dom/-/runtime-dom-3.5.16.tgz", "@vue/server-renderer@3.4.21": "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.4.21.tgz", "@vue/server-renderer@3.5.16": "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.5.16.tgz", "@vue/shared@3.4.21": "https://registry.npmjs.org/@vue/shared/-/shared-3.4.21.tgz", "@vue/shared@3.5.16": "https://registry.npmjs.org/@vue/shared/-/shared-3.5.16.tgz", "accepts@~1.3.8": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "acorn@^8.14.0": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "acorn@^8.14.1": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "adm-zip@^0.5.12": "https://registry.npmjs.org/adm-zip/-/adm-zip-0.5.16.tgz", "ansi-styles@^4.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "any-base@^1.1.0": "https://registry.npmjs.org/any-base/-/any-base-1.1.0.tgz", "anymatch@~3.1.2": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "array-flatten@1.1.1": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "array-union@^2.1.0": "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz", "at-least-node@^1.0.0": "https://registry.npmjs.org/at-least-node/-/at-least-node-1.0.0.tgz", "autoprefixer@^10.2.5": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz", "autoprefixer@^10.4.19": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz", "balanced-match@^1.0.0": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "base64-js@^1.3.1": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "base64url@^3.0.1": "https://registry.npmjs.org/base64url/-/base64url-3.0.1.tgz", "binary-extensions@^2.0.0": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz", "bmp-js@^0.1.0": "https://registry.npmjs.org/bmp-js/-/bmp-js-0.1.0.tgz", "body-parser@1.20.3": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.3.tgz", "brace-expansion@^1.1.7": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "braces@^3.0.3": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "braces@~3.0.2": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "browserslist@^4.24.0": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.0.tgz", "browserslist@^4.24.4": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.0.tgz", "buffer-equal@0.0.1": "https://registry.npmjs.org/buffer-equal/-/buffer-equal-0.0.1.tgz", "buffer@^5.2.0": "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz", "bytes@3.1.2": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "cac@^6.7.3": "https://registry.npmjs.org/cac/-/cac-6.7.14.tgz", "call-bind-apply-helpers@^1.0.1": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bind-apply-helpers@^1.0.2": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "call-bound@^1.0.2": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "caniuse-lite@^1.0.30001702": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001723.tgz", "caniuse-lite@^1.0.30001718": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001723.tgz", "centra@^2.7.0": "https://registry.npmjs.org/centra/-/centra-2.7.0.tgz", "chalk@^4.1.1": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chokidar@^3.5.3": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz", "chokidar@^4.0.0": "https://registry.npmjs.org/chokidar/-/chokidar-4.0.3.tgz", "color-convert@^2.0.1": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "color-name@~1.1.4": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "colorette@^1.1.0": "https://registry.npmjs.org/colorette/-/colorette-1.4.0.tgz", "compare-versions@^3.6.0": "https://registry.npmjs.org/compare-versions/-/compare-versions-3.6.0.tgz", "concat-map@0.0.1": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "confbox@^0.1.8": "https://registry.npmjs.org/confbox/-/confbox-0.1.8.tgz", "confbox@^0.2.1": "https://registry.npmjs.org/confbox/-/confbox-0.2.2.tgz", "content-disposition@0.5.4": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz", "content-type@~1.0.4": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "content-type@~1.0.5": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "convert-source-map@^2.0.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "cookie-signature@1.0.6": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "cookie@0.7.1": "https://registry.npmjs.org/cookie/-/cookie-0.7.1.tgz", "core-js@^3.4.1": "https://registry.npmjs.org/core-js/-/core-js-3.43.0.tgz", "cssesc@^3.0.0": "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz", "csstype@^3.1.3": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "debug@2.6.9": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@^4.1.0": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.1": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.3": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "depd@2.0.0": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "destroy@1.2.0": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "detect-libc@^1.0.3": "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz", "dir-glob@^3.0.1": "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz", "dom-walk@^0.1.0": "https://registry.npmjs.org/dom-walk/-/dom-walk-0.1.2.tgz", "dunder-proto@^1.0.1": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "ee-first@1.1.1": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "electron-to-chromium@^1.5.160": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.167.tgz", "encodeurl@~1.0.2": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "encodeurl@~2.0.0": "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz", "entities@^4.5.0": "https://registry.npmjs.org/entities/-/entities-4.5.0.tgz", "es-define-property@^1.0.1": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "es-errors@^1.3.0": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "es-module-lexer@^1.2.1": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.7.0.tgz", "es-object-atoms@^1.0.0": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "es-object-atoms@^1.1.1": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "esbuild@^0.18.10": "https://registry.npmjs.org/esbuild/-/esbuild-0.18.20.tgz", "esbuild@^0.20.1": "https://registry.npmjs.org/esbuild/-/esbuild-0.20.2.tgz", "escalade@^3.2.0": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "escape-html@~1.0.3": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "escape-string-regexp@^5.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-5.0.0.tgz", "estree-walker@^2.0.1": "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz", "estree-walker@^2.0.2": "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz", "estree-walker@^3.0.3": "https://registry.npmjs.org/estree-walker/-/estree-walker-3.0.3.tgz", "etag@~1.8.1": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "exif-parser@^0.1.12": "https://registry.npmjs.org/exif-parser/-/exif-parser-0.1.12.tgz", "express@^4.17.1": "https://registry.npmjs.org/express/-/express-4.21.2.tgz", "exsolve@^1.0.1": "https://registry.npmjs.org/exsolve/-/exsolve-1.0.5.tgz", "fast-glob@^3.0.3": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "fast-glob@^3.2.11": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "fast-glob@^3.3.3": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "fastq@^1.6.0": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz", "file-type@^9.0.0": "https://registry.npmjs.org/file-type/-/file-type-9.0.0.tgz", "fill-range@^7.1.1": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "finalhandler@1.3.1": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.3.1.tgz", "follow-redirects@^1.15.6": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "forwarded@0.2.0": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "fraction.js@^4.3.7": "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz", "fresh@0.5.2": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "fs-extra@^10.0.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "fs-extra@^8.1.0": "https://registry.npmjs.org/fs-extra/-/fs-extra-8.1.0.tgz", "fs-extra@^9.0.1": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz", "fs.realpath@^1.0.0": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "fsevents@~2.3.2": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "function-bind@^1.1.2": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "generic-names@^4.0.0": "https://registry.npmjs.org/generic-names/-/generic-names-4.0.0.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "get-intrinsic@^1.2.5": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-intrinsic@^1.3.0": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "get-proto@^1.0.1": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "glob-parent@^5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob-parent@~5.1.2": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "glob@^7.1.3": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "global@~4.4.0": "https://registry.npmjs.org/global/-/global-4.4.0.tgz", "globals@^11.1.0": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "globby@10.0.1": "https://registry.npmjs.org/globby/-/globby-10.0.1.tgz", "gopd@^1.2.0": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "graceful-fs@^4.1.6": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.0": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "has-flag@^4.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "has-symbols@^1.1.0": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "hash-sum@^2.0.0": "https://registry.npmjs.org/hash-sum/-/hash-sum-2.0.0.tgz", "hasown@^2.0.2": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "http-errors@2.0.0": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "iconv-lite@0.4.24": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "icss-replace-symbols@^1.1.0": "https://registry.npmjs.org/icss-replace-symbols/-/icss-replace-symbols-1.1.0.tgz", "icss-utils@^5.0.0": "https://registry.npmjs.org/icss-utils/-/icss-utils-5.1.0.tgz", "ieee754@^1.1.13": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "ignore@^5.1.1": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz", "immutable@^5.0.2": "https://registry.npmjs.org/immutable/-/immutable-5.1.3.tgz", "inflight@^1.0.4": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@2.0.4": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "invert-kv@^3.0.0": "https://registry.npmjs.org/invert-kv/-/invert-kv-3.0.1.tgz", "ipaddr.js@1.9.1": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "is-binary-path@~2.1.0": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz", "is-core-module@^2.16.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "is-extglob@^2.1.1": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "is-function@^1.0.1": "https://registry.npmjs.org/is-function/-/is-function-1.0.2.tgz", "is-glob@^4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@^4.0.3": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-glob@~4.0.1": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "is-number@^7.0.0": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "is-plain-object@^3.0.0": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-3.0.1.tgz", "isbinaryfile@^5.0.2": "https://registry.npmjs.org/isbinaryfile/-/isbinaryfile-5.0.4.tgz", "jimp@^0.10.1": "https://registry.npmjs.org/jimp/-/jimp-0.10.3.tgz", "jpeg-js@^0.3.4": "https://registry.npmjs.org/jpeg-js/-/jpeg-js-0.3.7.tgz", "js-tokens@^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-tokens@^9.0.1": "https://registry.npmjs.org/js-tokens/-/js-tokens-9.0.1.tgz", "jsesc@^3.0.2": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "json5@^2.2.3": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "jsonc-parser@^3.0.0": "https://registry.npmjs.org/jsonc-parser/-/jsonc-parser-3.3.1.tgz", "jsonc-parser@^3.2.0": "https://registry.npmjs.org/jsonc-parser/-/jsonc-parser-3.3.1.tgz", "jsonfile@^4.0.0": "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz", "jsonfile@^6.0.1": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "lcid@^3.0.0": "https://registry.npmjs.org/lcid/-/lcid-3.1.1.tgz", "licia@^1.29.0": "https://registry.npmjs.org/licia/-/licia-1.48.0.tgz", "lilconfig@^2.0.5": "https://registry.npmjs.org/lilconfig/-/lilconfig-2.1.0.tgz", "lines-and-columns@^2.0.4": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-2.0.4.tgz", "load-bmfont@^1.3.1": "https://registry.npmjs.org/load-bmfont/-/load-bmfont-1.4.2.tgz", "load-bmfont@^1.4.0": "https://registry.npmjs.org/load-bmfont/-/load-bmfont-1.4.2.tgz", "loader-utils@^3.2.0": "https://registry.npmjs.org/loader-utils/-/loader-utils-3.3.1.tgz", "local-pkg@^1.0.0": "https://registry.npmjs.org/local-pkg/-/local-pkg-1.1.1.tgz", "localstorage-polyfill@^1.0.1": "https://registry.npmjs.org/localstorage-polyfill/-/localstorage-polyfill-1.0.1.tgz", "lodash.camelcase@^4.3.0": "https://registry.npmjs.org/lodash.camelcase/-/lodash.camelcase-4.3.0.tgz", "lru-cache@^5.1.1": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "magic-string@^0.30.17": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "magic-string@^0.30.7": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "math-intrinsics@^1.1.0": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "media-typer@0.3.0": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "merge-descriptors@1.0.3": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.3.tgz", "merge2@^1.2.3": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "merge2@^1.3.0": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "merge@^2.1.1": "https://registry.npmjs.org/merge/-/merge-2.1.1.tgz", "methods@~1.1.2": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "micromatch@^4.0.5": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "micromatch@^4.0.8": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "mime-db@1.52.0": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "mime-types@~2.1.24": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.34": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime@1.6.0": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "mime@^1.3.4": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "mime@^2.5.2": "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz", "mime@^3.0.0": "https://registry.npmjs.org/mime/-/mime-3.0.0.tgz", "min-document@^2.19.0": "https://registry.npmjs.org/min-document/-/min-document-2.19.0.tgz", "minimatch@^3.1.1": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimist@^1.2.6": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "mkdirp@^0.5.1": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz", "mlly@^1.7.4": "https://registry.npmjs.org/mlly/-/mlly-1.7.4.tgz", "module-alias@^2.2.2": "https://registry.npmjs.org/module-alias/-/module-alias-2.2.3.tgz", "ms@2.0.0": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "ms@2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "nanoid@^3.3.11": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "negotiator@0.6.3": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "node-addon-api@^7.0.0": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-7.1.1.tgz", "node-releases@^2.0.19": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "normalize-path@^3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-path@~3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "normalize-range@^0.1.2": "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz", "object-inspect@^1.13.3": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz", "omggif@^1.0.9": "https://registry.npmjs.org/omggif/-/omggif-1.0.10.tgz", "on-finished@2.4.1": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "once@^1.3.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "os-locale-s-fix@^1.0.8-fix-1": "https://registry.npmjs.org/os-locale-s-fix/-/os-locale-s-fix-1.0.8-fix-1.tgz", "pako@^1.0.5": "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz", "parse-bmfont-ascii@^1.0.3": "https://registry.npmjs.org/parse-bmfont-ascii/-/parse-bmfont-ascii-1.0.6.tgz", "parse-bmfont-binary@^1.0.5": "https://registry.npmjs.org/parse-bmfont-binary/-/parse-bmfont-binary-1.0.6.tgz", "parse-bmfont-xml@^1.1.4": "https://registry.npmjs.org/parse-bmfont-xml/-/parse-bmfont-xml-1.1.6.tgz", "parse-headers@^2.0.0": "https://registry.npmjs.org/parse-headers/-/parse-headers-2.0.6.tgz", "parseurl@~1.3.3": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "path-is-absolute@^1.0.0": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "path-parse@^1.0.7": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "path-to-regexp@0.1.12": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.12.tgz", "path-type@^4.0.0": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz", "pathe@^2.0.1": "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz", "pathe@^2.0.2": "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz", "pathe@^2.0.3": "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz", "phin@^2.9.1": "https://registry.npmjs.org/phin/-/phin-2.9.3.tgz", "phin@^3.7.1": "https://registry.npmjs.org/phin/-/phin-3.7.1.tgz", "picocolors@^1.0.0": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "picocolors@^1.1.1": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "picomatch@^2.0.4": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.2": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.3.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^4.0.2": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz", "pify@^2.3.0": "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz", "pixelmatch@^4.0.2": "https://registry.npmjs.org/pixelmatch/-/pixelmatch-4.0.2.tgz", "pkg-types@^1.3.0": "https://registry.npmjs.org/pkg-types/-/pkg-types-1.3.1.tgz", "pkg-types@^1.3.1": "https://registry.npmjs.org/pkg-types/-/pkg-types-1.3.1.tgz", "pkg-types@^2.0.1": "https://registry.npmjs.org/pkg-types/-/pkg-types-2.1.0.tgz", "pngjs@^3.0.0": "https://registry.npmjs.org/pngjs/-/pngjs-3.4.0.tgz", "pngjs@^3.3.3": "https://registry.npmjs.org/pngjs/-/pngjs-3.4.0.tgz", "postcss-import@^14.0.2": "https://registry.npmjs.org/postcss-import/-/postcss-import-14.1.0.tgz", "postcss-load-config@^3.1.1": "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-3.1.4.tgz", "postcss-modules-extract-imports@^3.0.0": "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.1.0.tgz", "postcss-modules-local-by-default@^4.0.0": "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.2.0.tgz", "postcss-modules-scope@^3.0.0": "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-3.2.1.tgz", "postcss-modules-values@^4.0.0": "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz", "postcss-modules@^4.3.0": "https://registry.npmjs.org/postcss-modules/-/postcss-modules-4.3.1.tgz", "postcss-selector-parser@^6.0.4": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^6.0.6": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz", "postcss-selector-parser@^7.0.0": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-7.1.0.tgz", "postcss-value-parser@^4.0.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss-value-parser@^4.1.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss-value-parser@^4.2.0": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "postcss@^8.4.27": "https://registry.npmjs.org/postcss/-/postcss-8.5.5.tgz", "postcss@^8.4.35": "https://registry.npmjs.org/postcss/-/postcss-8.5.5.tgz", "postcss@^8.5.3": "https://registry.npmjs.org/postcss/-/postcss-8.5.5.tgz", "process@^0.11.10": "https://registry.npmjs.org/process/-/process-0.11.10.tgz", "proxy-addr@~2.0.7": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "qrcode-reader@^1.0.4": "https://registry.npmjs.org/qrcode-reader/-/qrcode-reader-1.0.4.tgz", "qrcode-terminal@^0.12.0": "https://registry.npmjs.org/qrcode-terminal/-/qrcode-terminal-0.12.0.tgz", "qs@6.13.0": "https://registry.npmjs.org/qs/-/qs-6.13.0.tgz", "quansync@^0.2.8": "https://registry.npmjs.org/quansync/-/quansync-0.2.10.tgz", "queue-microtask@^1.2.2": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "range-parser@~1.2.1": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "raw-body@2.5.2": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz", "read-cache@^1.0.0": "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz", "readdirp@^4.0.1": "https://registry.npmjs.org/readdirp/-/readdirp-4.1.2.tgz", "readdirp@~3.6.0": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "regenerator-runtime@^0.13.3": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "resolve@^1.1.7": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@^1.22.1": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "reusify@^1.0.4": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz", "rollup-plugin-copy@^3.4.0": "https://registry.npmjs.org/rollup-plugin-copy/-/rollup-plugin-copy-3.5.0.tgz", "rollup@^3.27.1": "https://registry.npmjs.org/rollup/-/rollup-3.29.5.tgz", "run-parallel@^1.1.9": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "safe-area-insets@^1.4.1": "https://registry.npmjs.org/safe-area-insets/-/safe-area-insets-1.4.1.tgz", "safe-buffer@5.2.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "safer-buffer@>= 2.1.2 < 3": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "sass@^1.89.0": "https://registry.npmjs.org/sass/-/sass-1.89.2.tgz", "sax@>=0.6.0": "https://registry.npmjs.org/sax/-/sax-1.4.1.tgz", "scule@^1.3.0": "https://registry.npmjs.org/scule/-/scule-1.3.0.tgz", "semver@^6.3.1": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "send@0.19.0": "https://registry.npmjs.org/send/-/send-0.19.0.tgz", "serve-static@1.16.2": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz", "setprototypeof@1.2.0": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "side-channel-list@^1.0.0": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "side-channel-map@^1.0.1": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz", "side-channel-weakmap@^1.0.2": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "side-channel@^1.0.6": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "slash@^3.0.0": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "source-map-js@>=0.6.2 <2.0.0": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "source-map-js@^1.0.2": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "source-map-js@^1.2.1": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "source-map@0.6.1": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "statuses@2.0.1": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "string-hash@^1.1.1": "https://registry.npmjs.org/string-hash/-/string-hash-1.1.3.tgz", "strip-literal@^3.0.0": "https://registry.npmjs.org/strip-literal/-/strip-literal-3.0.0.tgz", "supports-color@^7.1.0": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "supports-preserve-symlinks-flag@^1.0.0": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "tapable@^2.2.0": "https://registry.npmjs.org/tapable/-/tapable-2.2.2.tgz", "timm@^1.6.1": "https://registry.npmjs.org/timm/-/timm-1.7.1.tgz", "tinycolor2@^1.4.1": "https://registry.npmjs.org/tinycolor2/-/tinycolor2-1.6.0.tgz", "to-regex-range@^5.0.1": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "toidentifier@1.0.1": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "type-is@~1.6.18": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "ufo@^1.5.4": "https://registry.npmjs.org/ufo/-/ufo-1.6.1.tgz", "undici-types@~7.8.0": "https://registry.npmjs.org/undici-types/-/undici-types-7.8.0.tgz", "unimport@4.1.1": "https://registry.npmjs.org/unimport/-/unimport-4.1.1.tgz", "unimport@^4.1.1": "https://registry.npmjs.org/unimport/-/unimport-4.1.1.tgz", "universalify@^0.1.0": "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz", "universalify@^2.0.0": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "unpipe@1.0.0": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "unpipe@~1.0.0": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "unplugin-auto-import@19.1.0": "https://registry.npmjs.org/unplugin-auto-import/-/unplugin-auto-import-19.1.0.tgz", "unplugin-utils@^0.2.3": "https://registry.npmjs.org/unplugin-utils/-/unplugin-utils-0.2.4.tgz", "unplugin-utils@^0.2.4": "https://registry.npmjs.org/unplugin-utils/-/unplugin-utils-0.2.4.tgz", "unplugin@^2.1.2": "https://registry.npmjs.org/unplugin/-/unplugin-2.3.5.tgz", "unplugin@^2.2.0": "https://registry.npmjs.org/unplugin/-/unplugin-2.3.5.tgz", "update-browserslist-db@^1.1.3": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "utif@^2.0.1": "https://registry.npmjs.org/utif/-/utif-2.0.1.tgz", "util-deprecate@^1.0.2": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "utils-merge@1.0.1": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "vary@~1.1.2": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "vite@^4.5.0": "https://registry.npmjs.org/vite/-/vite-4.5.14.tgz", "vue-router@^4.3.0": "https://registry.npmjs.org/vue-router/-/vue-router-4.5.1.tgz", "vue@^3.3.8": "https://registry.npmjs.org/vue/-/vue-3.5.16.tgz", "webpack-virtual-modules@^0.6.2": "https://registry.npmjs.org/webpack-virtual-modules/-/webpack-virtual-modules-0.6.2.tgz", "wrappy@1": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "ws@^8.4.2": "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz", "xhr@^2.0.1": "https://registry.npmjs.org/xhr/-/xhr-2.6.0.tgz", "xml-parse-from-string@^1.0.0": "https://registry.npmjs.org/xml-parse-from-string/-/xml-parse-from-string-1.0.1.tgz", "xml2js@^0.5.0": "https://registry.npmjs.org/xml2js/-/xml2js-0.5.0.tgz", "xmlbuilder@~11.0.0": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz", "xmlhttprequest@^1.8.0": "https://registry.npmjs.org/xmlhttprequest/-/xmlhttprequest-1.8.0.tgz", "xregexp@3.1.0": "https://registry.npmjs.org/xregexp/-/xregexp-3.1.0.tgz", "xtend@^4.0.0": "https://registry.npmjs.org/xtend/-/xtend-4.0.2.tgz", "yallist@^3.0.2": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "yaml@^1.10.2": "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"}, "files": [], "artifacts": {}}