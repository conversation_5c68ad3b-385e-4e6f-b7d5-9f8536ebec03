<template>
	<div class="dashboard">
		<!-- 头部统计卡片 -->
		<div class="header-card">
			<div class="merchant-info">
				<img class="merchant-logo" :src="merchantInfo.avatar || '/static/images/default-logo.png'" alt="logo" />
				<div class="merchant-detail">
					<span class="merchant-name">{{merchantInfo.name || '加载中...'}}</span>
					<span class="merchant-status" :class="merchantInfo.status === '1' ? 'status-normal' : 'status-warning'">
						{{merchantInfo.status === '1' ? '营业中' : '暂停营业'}}
					</span>
				</div>
			</div>
			
			<div class="stats-container">
				<div class="stat-item">
					<span class="stat-value">{{dashboardData.today_income || '0.00'}}</span>
					<span class="stat-label">今日收入(元)</span>
				</div>
				<div class="stat-item">
					<span class="stat-value">{{dashboardData.today_orders || '0'}}</span>
					<span class="stat-label">今日订单</span>
				</div>
				<div class="stat-item">
					<span class="stat-value">{{dashboardData.today_reservations || '0'}}</span>
					<span class="stat-label">今日预约</span>
				</div>
			</div>
		</div>
		
		<!-- 快捷功能区 -->
		<div class="quick-actions">
			<div class="section-title">快捷功能</div>
			<div class="actions-grid">
				<div class="action-item" @click="navigateTo('/appointment')">
					<div class="action-icon order-icon">📋</div>
					<span class="action-text">预约订单</span>
				</div>
				<div class="action-item" @click="navigateTo('/table')">
					<div class="action-icon table-icon">🎱</div>
					<span class="action-text">台球桌管理</span>
				</div>
				<div class="action-item" @click="navigateTo('/coach')">
					<div class="action-icon coach-icon">👨‍🏫</div>
					<span class="action-text">教练管理</span>
				</div>
				<div class="action-item" @click="navigateTo('/pages/coupon/coupon')">
					<div class="action-icon coupon-icon">🎫</div>
					<span class="action-text">优惠券</span>
				</div>
				<div class="action-item" @click="navigateTo('/mine')">
					<div class="action-icon schedule-icon">👤</div>
					<span class="action-text">个人中心</span>
				</div>
			</div>
		</div>
		
		<!-- 待处理事项 -->
		<div class="pending-tasks">
			<div class="section-title">待处理事项</div>
			<div class="task-list">
				<div class="task-item" v-for="(task, index) in pendingTasks" :key="index" @click="handleTask(task)">
					<div class="task-tag" :class="'task-'+task.type">{{task.typeText}}</div>
					<div class="task-content">
						<span class="task-title">{{task.title}}</span>
						<span class="task-time">{{task.time}}</span>
					</div>
					<span class="task-arrow">▶</span>
				</div>
				
				<div class="empty-tip" v-if="pendingTasks.length === 0">
					<div class="empty-icon">📋</div>
					<span class="empty-text">暂无待办事项</span>
				</div>
			</div>
		</div>
		
		<!-- 加载状态 -->
		<div class="loading-mask" v-if="loading">
			<div class="loading-content">
				<div class="loading-spinner"></div>
				<span>加载中...</span>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { getMerchantInfo } from '../../api/merchant/auth.js';
import { getDashboardOverview } from '../../api/merchant/stats.js';
import { getBookingList } from '../../api/merchant/booking.js';

const router = useRouter();

// 响应式数据
const merchantInfo = ref({
	name: '',
	avatar: '',
	status: '1'
});

const dashboardData = ref({
	today_income: '0.00',
	today_orders: '0',
	today_reservations: '0'
});

const pendingTasks = ref([]);
const loading = ref(true);

// 页面加载时
onMounted(() => {
	loadDashboardData();
});

// 加载仪表板数据
const loadDashboardData = async () => {
	try {
		loading.value = true;
		
		// 并行加载商家信息和仪表板数据
		const [merchantRes, dashboardRes] = await Promise.all([
			getMerchantInfo(),
			getDashboardOverview()
		]);
		
		// 更新商家信息
		if (merchantRes && merchantRes.data) {
			merchantInfo.value = {
				name: merchantRes.data.name,
				avatar: merchantRes.data.avatar,
				status: merchantRes.data.status
			};
		}
		
		// 更新仪表板数据
		if (dashboardRes && dashboardRes.data) {
			dashboardData.value = {
				today_income: dashboardRes.data.today_income || '0.00',
				today_orders: dashboardRes.data.today_orders || '0',
				today_reservations: dashboardRes.data.today_reservations || '0'
			};
		}
		
		// 加载待处理事项
		await loadPendingTasks();
		
	} catch (error) {
		console.error('加载仪表板数据失败', error);
		uni.showToast({
			title: '数据加载失败',
			icon: 'none'
		});
		
		// 设置默认数据
		merchantInfo.value = {
			name: '台球馆商家',
			avatar: '',
			status: '1'
		};
		
	} finally {
		loading.value = false;
	}
};

// 加载待处理事项
const loadPendingTasks = async () => {
	try {
		// 获取待确认的预订
		const bookingRes = await getBookingList({
			status: 'pending',
			page: 1,
			pageSize: 10
		});
		
		if (bookingRes && bookingRes.data && bookingRes.data.list) {
			const tasks = bookingRes.data.list.map(booking => ({
				id: booking.id,
				type: 'appointment',
				typeText: '预约',
				title: `${booking.type === 'table' ? '台桌' : '教练'}预约待确认`,
				time: formatTime(booking.created_at),
				data: booking
			}));
			
			pendingTasks.value = tasks;
		}
		
	} catch (error) {
		console.error('加载待处理事项失败', error);
		pendingTasks.value = [];
	}
};

// 时间格式化
const formatTime = (timestamp) => {
	if (!timestamp) return '';
	
	const now = Date.now();
	const time = new Date(timestamp * 1000).getTime();
	const diff = now - time;
	
	if (diff < 60000) {
		return '刚刚';
	} else if (diff < 3600000) {
		return `${Math.floor(diff / 60000)}分钟前`;
	} else if (diff < 86400000) {
		return `${Math.floor(diff / 3600000)}小时前`;
	} else {
		return `${Math.floor(diff / 86400000)}天前`;
	}
};

// 导航到指定页面
const navigateTo = (url) => {
	// 检查是否是tabBar页面
	const tabBarPages = ['/appointment', '/table', '/coach', '/mine'];

	if (tabBarPages.includes(url)) {
		uni.switchTab({
			url: `/pages${url}${url}`
		});
	} else {
		uni.navigateTo({
			url: url
		});
	}
};

// 处理待办任务
const handleTask = (task) => {
	if (task.type === 'appointment') {
		navigateTo('/appointment');
	} else if (task.type === 'refund') {
		navigateTo('/appointment');
	}
};
</script>

<style lang="scss">
	.dashboard {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.header-card {
		background-color: #007AFF;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
	}
	
	.merchant-info {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.merchant-logo {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #FFF;
		margin-right: 20rpx;
	}
	
	.merchant-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #FFFFFF;
		margin-bottom: 6rpx;
		display: block;
	}
	
	.merchant-status {
		font-size: 24rpx;
		padding: 4rpx 16rpx;
		border-radius: 20rpx;
		display: inline-block;
	}
	
	.status-normal {
		background-color: #00CC66;
		color: #FFFFFF;
	}
	
	.status-warning {
		background-color: #FF9500;
		color: #FFFFFF;
	}
	
	.stats-container {
		display: flex;
		justify-content: space-between;
		border-top: 1rpx solid rgba(255, 255, 255, 0.2);
		padding-top: 20rpx;
	}
	
	.stat-item {
		flex: 1;
		text-align: center;
	}
	
	.stat-value {
		font-size: 36rpx;
		font-weight: bold;
		color: #FFFFFF;
		display: block;
		margin-bottom: 6rpx;
	}
	
	.stat-label {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
		padding-left: 10rpx;
		border-left: 8rpx solid #007AFF;
	}
	
	.quick-actions, .pending-tasks {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.actions-grid {
		display: flex;
		flex-wrap: wrap;
	}
	
	.action-item {
		width: 25%;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx 0;
		cursor: pointer;
	}
	
	.action-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #F0F7FF;
		margin-bottom: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.order-icon {
		background-color: #FFF3E0;
	}
	
	.table-icon {
		background-color: #E3F8FF;
	}
	
	.coach-icon {
		background-color: #EBFFEC;
	}
	
	.schedule-icon {
		background-color: #FFF2F7;
	}

	.coupon-icon {
		background-color: #FFF8E1;
	}
	
	.action-text {
		font-size: 24rpx;
		color: #333333;
	}
	
	.task-list {
		background-color: #FFFFFF;
		border-radius: 8rpx;
	}
	
	.task-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #F5F5F5;
		cursor: pointer;
	}
	
	.task-tag {
		padding: 6rpx 12rpx;
		border-radius: 6rpx;
		font-size: 24rpx;
		margin-right: 20rpx;
	}
	
	.task-appointment {
		background-color: #E3F8FF;
		color: #0091FF;
	}
	
	.task-refund {
		background-color: #FFF3E0;
		color: #FF9500;
	}
	
	.task-content {
		flex: 1;
	}
	
	.task-title {
		font-size: 28rpx;
		color: #333333;
		display: block;
	}
	
	.task-time {
		font-size: 24rpx;
		color: #999999;
	}
	
	.task-arrow {
		font-size: 16rpx;
		color: #BBBBBB;
	}
	
	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40rpx 0;
	}
	
	.empty-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 60rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
	
	.loading-mask {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.loading-content {
		background-color: #FFFFFF;
		padding: 40rpx;
		border-radius: 16rpx;
		text-align: center;
	}
	
	.loading-spinner {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 20rpx;
		border: 4rpx solid #007AFF;
		border-top-color: transparent;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}
	
	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}
</style>
