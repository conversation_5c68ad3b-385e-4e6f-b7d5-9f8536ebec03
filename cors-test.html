<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .result {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007AFF;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            border-left-color: #ff3b30;
            background: #fff5f5;
        }
        .success {
            border-left-color: #28a745;
            background: #f5fff5;
        }
    </style>
</head>
<body>
    <h1>🔧 CORS配置测试工具</h1>
    
    <div class="test-section">
        <h2>基础连通性测试</h2>
        <button onclick="testSimpleRequest()">测试简单GET请求</button>
        <button onclick="testPreflightRequest()">测试预检POST请求</button>
        <button onclick="testWithHeaders()">测试带自定义头的请求</button>
    </div>
    
    <div class="test-section">
        <h2>商家登录API测试</h2>
        <button onclick="testMerchantLogin()">测试商家登录接口</button>
    </div>
    
    <div id="results"></div>

    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(title, content, isError = false) {
            const div = document.createElement('div');
            div.className = `result ${isError ? 'error' : 'success'}`;
            div.innerHTML = `<strong>${title}</strong>\n${content}`;
            resultsDiv.appendChild(div);
        }
        
        // 测试简单GET请求
        async function testSimpleRequest() {
            try {
                const response = await fetch('http://localhost/api/merchant/auth/info', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.text();
                addResult('✅ 简单GET请求', `状态码: ${response.status}\n响应: ${data}`);
            } catch (error) {
                addResult('❌ 简单GET请求失败', error.message, true);
            }
        }
        
        // 测试预检POST请求
        async function testPreflightRequest() {
            try {
                const response = await fetch('http://localhost/api/merchant/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phone: '13800138000',
                        password: '123456'
                    })
                });
                
                const data = await response.text();
                addResult('✅ 预检POST请求', `状态码: ${response.status}\n响应: ${data}`);
            } catch (error) {
                addResult('❌ 预检POST请求失败', error.message, true);
            }
        }
        
        // 测试带自定义头的请求
        async function testWithHeaders() {
            try {
                const response = await fetch('http://localhost/api/merchant/auth/info', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-token',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                
                const data = await response.text();
                addResult('✅ 带自定义头请求', `状态码: ${response.status}\n响应: ${data}`);
            } catch (error) {
                addResult('❌ 带自定义头请求失败', error.message, true);
            }
        }
        
        // 测试商家登录
        async function testMerchantLogin() {
            try {
                const response = await fetch('http://localhost/api/merchant/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        phone: '13800138000',
                        password: '123456'
                    })
                });
                
                const data = await response.json();
                addResult('✅ 商家登录API', `状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                addResult('❌ 商家登录API失败', error.message, true);
            }
        }
    </script>
</body>
</html> 