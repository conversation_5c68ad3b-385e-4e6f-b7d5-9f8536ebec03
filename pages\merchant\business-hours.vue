<template>
	<view class="business-hours-container">
		<view class="tips-card">
			<uni-icons type="info" size="16" color="#007AFF"></uni-icons>
			<text class="tips-text">设置营业时间后，顾客只能在营业时间内预约</text>
		</view>
		
		<!-- 营业时间设置 -->
		<view class="setting-card">
			<view class="card-title">
				<text>营业时间设置</text>
				<text class="week-text">{{getCurrentWeek()}}</text>
			</view>
			
			<view class="time-settings">
				<view class="day-item" v-for="(day, index) in businessHours" :key="index">
					<view class="day-header">
						<text class="day-name">{{day.name}}</text>
						<switch :checked="day.isOpen" @change="toggleDay(index)" color="#007AFF"/>
					</view>
					
					<view class="time-slots" v-if="day.isOpen">
						<view class="time-slot" v-for="(slot, slotIndex) in day.timeSlots" :key="slotIndex">
							<view class="time-picker" @click="showTimePicker(index, slotIndex, 'start')">
								<text>{{slot.start}}</text>
								<uni-icons type="calendar" size="14" color="#999999"></uni-icons>
							</view>
							<text class="separator">至</text>
							<view class="time-picker" @click="showTimePicker(index, slotIndex, 'end')">
								<text>{{slot.end}}</text>
								<uni-icons type="calendar" size="14" color="#999999"></uni-icons>
							</view>
							<view class="action-btn" @click="removeTimeSlot(index, slotIndex)" v-if="day.timeSlots.length > 1">
								<uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
							</view>
						</view>
						
						<view class="add-time-btn" @click="addTimeSlot(index)" v-if="day.timeSlots.length < 3">
							<uni-icons type="plusempty" size="16" color="#007AFF"></uni-icons>
							<text>添加时间段</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 是否24小时营业 -->
		<view class="option-card">
			<text class="option-text">24小时营业</text>
			<switch :checked="is24Hours" @change="toggle24Hours" color="#007AFF"/>
		</view>
		
		<!-- 保存按钮 -->
		<view class="bottom-actions">
			<button class="save-btn" @click="saveSettings">保存</button>
		</view>
		
		<!-- 时间选择器弹窗 -->
		<uni-popup ref="timePicker" type="bottom">
			<view class="time-picker-content">
				<view class="picker-header">
					<text class="cancel-btn" @click="cancelTimePicker">取消</text>
					<text class="confirm-btn" @click="confirmTimePicker">确定</text>
				</view>
				<picker-view class="picker-view" :value="selectedTimeArr" @change="timePickerChange">
					<picker-view-column>
						<view class="picker-item" v-for="hour in 24" :key="hour">{{(hour-1).toString().padStart(2, '0')}}</view>
					</picker-view-column>
					<picker-view-column>
						<view class="picker-item" v-for="minute in [0, 30]" :key="minute">{{minute.toString().padStart(2, '0')}}</view>
					</picker-view-column>
				</picker-view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 响应式数据
const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

// 初始化营业时间数据
const businessHours = ref(weekDays.map(name => ({
	name,
	isOpen: true,
	timeSlots: [
		{
			start: '10:00',
			end: '22:00'
		}
	]
})));

const is24Hours = ref(false);

// 时间选择器相关
const currentEditingDay = ref(0);
const currentEditingSlot = ref(0);
const currentEditingField = ref('start');
const selectedTimeArr = ref([10, 0]); // 默认选择10:00
const timePicker = ref(null);

// 页面加载时
onMounted(() => {
	// 加载营业时间设置
	loadBusinessHours();
});

// 获取当前周几
const getCurrentWeek = () => {
	const date = new Date();
	const day = date.getDay() || 7; // 转换为周一到周日（1-7）
	return `本周${businessHours.value[day-1].name}`;
};

// 加载营业时间设置
const loadBusinessHours = () => {
	// 从存储加载营业时间设置
	const settings = uni.getStorageSync('businessHours');
	if (settings) {
		const data = JSON.parse(settings);
		businessHours.value = data.businessHours;
		is24Hours.value = data.is24Hours;
	}
};

// 切换日期开关
const toggleDay = (index) => {
	businessHours.value[index].isOpen = !businessHours.value[index].isOpen;
};

// 切换24小时营业
const toggle24Hours = () => {
	is24Hours.value = !is24Hours.value;
	
	// 如果开启24小时营业，将所有时间段设为24小时
	if (is24Hours.value) {
		businessHours.value.forEach(day => {
			day.isOpen = true;
			day.timeSlots = [
				{
					start: '00:00',
					end: '24:00'
				}
			];
		});
	}
};

// 添加时间段
const addTimeSlot = (dayIndex) => {
	if (businessHours.value[dayIndex].timeSlots.length >= 3) {
		uni.showToast({
			title: '最多添加3个时间段',
			icon: 'none'
		});
		return;
	}
	
	businessHours.value[dayIndex].timeSlots.push({
		start: '10:00',
		end: '22:00'
	});
};

// 删除时间段
const removeTimeSlot = (dayIndex, slotIndex) => {
	if (businessHours.value[dayIndex].timeSlots.length <= 1) {
		uni.showToast({
			title: '至少保留一个时间段',
			icon: 'none'
		});
		return;
	}
	
	businessHours.value[dayIndex].timeSlots.splice(slotIndex, 1);
};

// 显示时间选择器
const showTimePicker = (dayIndex, slotIndex, field) => {
	currentEditingDay.value = dayIndex;
	currentEditingSlot.value = slotIndex;
	currentEditingField.value = field;
	
	// 设置初始选中时间
	const timeStr = businessHours.value[dayIndex].timeSlots[slotIndex][field];
	const [hours, minutes] = timeStr.split(':').map(Number);
	selectedTimeArr.value = [hours, minutes === 30 ? 1 : 0];
	
	timePicker.value.open();
};

// 时间选择器变化
const timePickerChange = (e) => {
	selectedTimeArr.value = e.detail.value;
};

// 取消时间选择
const cancelTimePicker = () => {
	timePicker.value.close();
};

// 确认时间选择
const confirmTimePicker = () => {
	const hours = selectedTimeArr.value[0];
	const minutes = selectedTimeArr.value[1] === 1 ? 30 : 0;
	const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
	
	businessHours.value[currentEditingDay.value].timeSlots[currentEditingSlot.value][currentEditingField.value] = timeStr;
	
	timePicker.value.close();
};

// 验证时间设置
const validateTimeSettings = () => {
	// 验证时间设置的合理性
	let isValid = true;
	
	businessHours.value.forEach((day, dayIndex) => {
		if (!day.isOpen) return;
		
		day.timeSlots.forEach((slot, slotIndex) => {
			// 检查结束时间是否大于开始时间
			const [startHours, startMinutes] = slot.start.split(':').map(Number);
			const [endHours, endMinutes] = slot.end.split(':').map(Number);
			
			const startTime = startHours * 60 + startMinutes;
			const endTime = endHours * 60 + endMinutes;
			
			if (startTime >= endTime) {
				isValid = false;
				uni.showToast({
					title: `${day.name}的结束时间必须晚于开始时间`,
					icon: 'none'
				});
			}
			
			// 检查时间段是否有重叠
			if (day.timeSlots.length > 1) {
				day.timeSlots.forEach((otherSlot, otherIndex) => {
					if (slotIndex === otherIndex) return;
					
					const [otherStartHours, otherStartMinutes] = otherSlot.start.split(':').map(Number);
					const [otherEndHours, otherEndMinutes] = otherSlot.end.split(':').map(Number);
					
					const otherStartTime = otherStartHours * 60 + otherStartMinutes;
					const otherEndTime = otherEndHours * 60 + otherEndMinutes;
					
					if ((startTime < otherEndTime && endTime > otherStartTime) ||
						(otherStartTime < endTime && otherEndTime > startTime)) {
						isValid = false;
						uni.showToast({
							title: `${day.name}的时间段不能重叠`,
							icon: 'none'
						});
					}
				});
			}
		});
	});
	
	return isValid;
};

// 保存设置
const saveSettings = () => {
	// 验证时间设置
	if (!validateTimeSettings()) {
		return;
	}
	
	// 保存设置
	uni.setStorageSync('businessHours', JSON.stringify({
		businessHours: businessHours.value,
		is24Hours: is24Hours.value
	}));
	
	uni.showToast({
		title: '保存成功',
		icon: 'success'
	});
	
	// 更新商家信息中的营业时间显示
	let businessHoursText = '';
	if (is24Hours.value) {
		businessHoursText = '24小时营业';
	} else {
		// 获取第一个营业的日期的第一个时间段
		for (let day of businessHours.value) {
			if (day.isOpen && day.timeSlots.length > 0) {
				businessHoursText = `${day.timeSlots[0].start}-${day.timeSlots[0].end}`;
				break;
			}
		}
	}
	
	// 返回上一页
	setTimeout(() => {
		uni.navigateBack();
	}, 1500);
};
</script>

<style lang="scss">
	.business-hours-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding: 20rpx;
		padding-bottom: 150rpx;
	}
	
	.tips-card {
		display: flex;
		align-items: center;
		background-color: #F0F7FF;
		padding: 20rpx;
		border-radius: 8rpx;
		margin-bottom: 20rpx;
	}
	
	.tips-text {
		font-size: 24rpx;
		color: #007AFF;
		margin-left: 10rpx;
	}
	
	.setting-card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 20rpx;
	}
	
	.card-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #F5F5F5;
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.week-text {
		font-size: 24rpx;
		color: #007AFF;
		font-weight: normal;
	}
	
	.day-item {
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #F5F5F5;
		
		&:last-child {
			border-bottom: none;
		}
	}
	
	.day-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.day-name {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
	}
	
	.time-slots {
		margin-left: 20rpx;
	}
	
	.time-slot {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.time-picker {
		flex: 1;
		height: 70rpx;
		background-color: #F5F5F5;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20rpx;
		font-size: 26rpx;
		color: #333333;
	}
	
	.separator {
		padding: 0 20rpx;
		color: #999999;
	}
	
	.action-btn {
		width: 70rpx;
		height: 70rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.add-time-btn {
		height: 70rpx;
		background-color: #F0F7FF;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 26rpx;
		color: #007AFF;
		
		text {
			margin-left: 10rpx;
		}
	}
	
	.option-card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.option-text {
		font-size: 28rpx;
		color: #333333;
	}
	
	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #FFFFFF;
		padding: 20rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.save-btn {
		background-color: #007AFF;
		color: #FFFFFF;
		border-radius: 40rpx;
		padding: 20rpx 0;
		font-size: 30rpx;
		text-align: center;
	}
	
	.time-picker-content {
		background-color: #FFFFFF;
		border-top-left-radius: 16rpx;
		border-top-right-radius: 16rpx;
	}
	
	.picker-header {
		display: flex;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #F5F5F5;
	}
	
	.cancel-btn, .confirm-btn {
		font-size: 28rpx;
		padding: 10rpx;
	}
	
	.cancel-btn {
		color: #999999;
	}
	
	.confirm-btn {
		color: #007AFF;
		font-weight: 500;
	}
	
	.picker-view {
		height: 500rpx;
	}
	
	.picker-item {
		line-height: 100rpx;
		text-align: center;
	}
</style> 