# 星界商家助手 - 商家端管理系统

基于 uniapp 开发的星界商家端管理系统，支持多端运行（H5、小程序、App）。

## 功能特性

- 🏓 **台桌管理** - 台桌状态控制、信息编辑、使用统计
- 👨‍🏫 **教练管理** - 教练信息、排班管理、绩效统计
- 📅 **预约订单** - 订单处理、状态管理、退款操作
- 📊 **数据统计** - 营业数据、收入分析、客户分析
- 👤 **商家信息** - 个人资料、营业设置、系统配置

## 技术栈

- **前端框架**: uniapp + Vue 3 + Composition API
- **UI 组件**: uni-ui
- **构建工具**: Vite
- **状态管理**: 本地存储 + API 数据同步
- **网络请求**: 统一封装的 request 工具

## 快速开始

### 环境要求
- Node.js >= 16
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发运行
```bash
# H5 端开发
npm run dev:h5

# 微信小程序
npm run dev:mp-weixin

# 支付宝小程序
npm run dev:mp-alipay

# Android App
npm run dev:app-android

# iOS App
npm run dev:app-ios
```

### 构建发布
```bash
# 构建 H5
npm run build:h5

# 构建微信小程序
npm run build:mp-weixin
```

## 项目结构

```
├── api/                    # API 接口定义
│   └── merchant/           # 商家端相关接口
│       ├── auth.js         # 认证接口
│       ├── table.js        # 台桌管理接口
│       ├── coach.js        # 教练管理接口
│       ├── booking.js      # 预订管理接口
│       └── stats.js        # 统计数据接口
├── pages/                  # 页面目录
│   ├── index/              # 首页
│   ├── login/              # 登录注册
│   ├── table/              # 台桌管理
│   ├── coach/              # 教练管理
│   ├── appointment/        # 预约订单
│   ├── mine/               # 个人中心
│   └── test/               # 测试页面
├── static/                 # 静态资源
├── utils/                  # 工具函数
│   └── request.js          # 网络请求工具
├── manifest.json           # 应用配置
├── pages.json              # 页面路由配置
└── vite.config.js          # 构建配置
```

## API 文档

项目包含完整的 API 接口定义，共 33 个接口，涵盖：

### 认证模块 (4个接口)
- 商家注册/登录
- 获取商家信息
- 退出登录

### 台桌管理 (7个接口)
- 台桌列表/详情
- 台桌增删改
- 状态设置
- 使用统计

### 教练管理 (9个接口)
- 教练列表/详情
- 教练增删改
- 状态设置
- 排班管理
- 收益统计

### 预订管理 (8个接口)
- 预订列表/详情
- 状态更新
- 取消退款
- 批量处理
- 数据导出

### 数据统计 (5个接口)
- 首页概览
- 收入统计
- 预订分析
- 客户分析
- 报表导出

## 测试功能

访问 `/pages/test/final-api-test` 页面可以测试所有 API 接口功能。

## 配置说明

### 1. API 基础地址配置
在 `utils/request.js` 中修改 `baseURL`：

```javascript
const baseURL = 'http://your-api-domain.com'
```

### 2. 跨域代理配置
开发环境下的代理配置在 `vite.config.js` 和 `manifest.json` 中：

```javascript
// vite.config.js
proxy: {
  '/api': {
    target: 'http://localhost:8000',
    changeOrigin: true
  }
}
```

### 3. 应用信息配置
在 `manifest.json` 中修改应用信息：

```json
{
  "name": "your-app-name",
  "appid": "__UNI__your_app_id",
  "description": "your app description"
}
```

## 部署指南

### H5 部署
1. 运行 `npm run build:h5`
2. 将 `dist/build/h5` 目录部署到 Web 服务器
3. 配置服务器代理解决跨域问题

### 小程序部署
1. 运行 `npm run build:mp-weixin`
2. 使用微信开发者工具打开 `dist/build/mp-weixin` 目录
3. 配置小程序信息并上传

### App 部署
1. 运行 `npm run build:app`
2. 使用 HBuilderX 打开项目
3. 配置原生应用信息并打包

## 注意事项

1. **CORS 问题**: 开发环境使用代理，生产环境需要后端配置 CORS
2. **Token 管理**: 已实现自动 token 管理和刷新机制
3. **错误处理**: 统一的错误处理，网络异常时会显示离线数据
4. **数据缓存**: 关键数据会缓存到本地，提升用户体验

## 开发建议

1. **接口调试**: 使用内置的 API 测试页面进行接口调试
2. **错误日志**: 查看控制台输出的详细错误信息
3. **样式调试**: 使用浏览器开发者工具调试 H5 端样式
4. **性能优化**: 注意图片大小、网络请求次数等性能因素

## 技术支持

如有问题，请查看：
1. 项目完成总结文档
2. API 接口文件中的注释
3. 控制台错误信息
4. uniapp 官方文档

## 更新日志

- v1.0.0 - 完成基础框架和所有核心功能
- 包含 5 大功能模块
- 33 个 API 接口
- 完整的页面交互和数据管理 