<template>
	<view class="coupon-container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input-box">
				<uni-icons type="search" size="18" color="#999"></uni-icons>
				<input class="search-input" type="text" v-model="searchKey" placeholder="搜索优惠券名称" @confirm="searchCoupons"/>
			</view>
			<button class="add-btn" @click="navigateTo('/pages/coupon/detail?type=add')">添加</button>
		</view>
		
		<!-- 过滤栏 -->
		<view class="filter-bar">
			<view class="filter-item" :class="{active: activeFilter === 'all'}" @click="filterCoupons('all')">
				全部
			</view>
			<view class="filter-item" :class="{active: activeFilter === 'active'}" @click="filterCoupons('active')">
				启用中
			</view>
			<view class="filter-item" :class="{active: activeFilter === 'inactive'}" @click="filterCoupons('inactive')">
				已禁用
			</view>
		</view>
		
		<!-- 优惠券列表 -->
		<view class="coupon-list">
			<view class="coupon-item" v-for="(coupon, index) in filteredCoupons" :key="index" @click="navigateTo('/pages/coupon/detail?id='+coupon.id)">
				<view class="coupon-card" :class="coupon.status === 1 ? 'card-active' : 'card-inactive'">
					<view class="coupon-header">
						<view class="coupon-type" :class="'type-' + coupon.type">
							{{getTypeText(coupon.type)}}
						</view>
						<view class="coupon-status" :class="coupon.status === 1 ? 'status-active' : 'status-inactive'">
							{{coupon.status === 1 ? '启用中' : '已禁用'}}
						</view>
					</view>
					
					<view class="coupon-content">
						<view class="coupon-name">{{coupon.name}}</view>
						<view class="coupon-desc">{{coupon.description_text}}</view>
						
						<view class="coupon-stats">
							<view class="stat-item">
								<text class="stat-label">已领取</text>
								<text class="stat-value">{{coupon.received_count}}</text>
							</view>
							<view class="stat-item">
								<text class="stat-label">已使用</text>
								<text class="stat-value">{{coupon.used_count}}</text>
							</view>
							<view class="stat-item">
								<text class="stat-label">总数量</text>
								<text class="stat-value">{{coupon.total_quantity}}</text>
							</view>
						</view>
						
						<view class="coupon-time">
							<text class="time-label">有效期：</text>
							<text class="time-value">{{formatDate(coupon.start_time)}} - {{formatDate(coupon.end_time)}}</text>
						</view>
					</view>
					
					<view class="coupon-actions">
						<view class="action-btn" @click.stop="toggleCouponStatus(coupon)">
							<uni-icons :type="coupon.status === 1 ? 'eye-slash' : 'eye'" size="16" :color="coupon.status === 1 ? '#FF9500' : '#00CC66'"></uni-icons>
							<text class="action-text">{{coupon.status === 1 ? '禁用' : '启用'}}</text>
						</view>
						<view class="action-btn" @click.stop="navigateTo('/pages/coupon/detail?id='+coupon.id+'&type=edit')">
							<uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
							<text class="action-text">编辑</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-tip" v-if="filteredCoupons.length === 0">
				<image class="empty-icon" src="/static/images/empty.png"></image>
				<text class="empty-text">暂无优惠券数据</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { getCouponList, toggleCouponStatus } from '../../api/merchant/coupon.js';

// 响应式数据
const searchKey = ref('');
const activeFilter = ref('all');
const coupons = ref([
	{
		id: 1,
		name: '满100减20优惠券',
		type: 1, // 1=满减券 2=折扣券 3=无门槛券
		discount_value: 20.00,
		min_amount: 100.00,
		total_quantity: 100,
		received_count: 45,
		used_count: 32,
		status: 1,
		start_time: '2025-01-01 00:00:00',
		end_time: '2025-01-31 23:59:59',
		description_text: '满100元减20元'
	},
	{
		id: 2,
		name: '新用户专享券',
		type: 3,
		discount_value: 10.00,
		min_amount: 0.00,
		total_quantity: 50,
		received_count: 20,
		used_count: 15,
		status: 1,
		start_time: '2025-01-01 00:00:00',
		end_time: '2025-12-31 23:59:59',
		description_text: '无门槛减10元'
	}
]);

// 计算属性
const filteredCoupons = computed(() => {
	let result = [...coupons.value];
	
	// 按搜索关键词过滤
	if (searchKey.value) {
		result = result.filter(coupon => coupon.name.includes(searchKey.value));
	}
	
	// 按状态过滤
	if (activeFilter.value === 'active') {
		result = result.filter(coupon => coupon.status === 1);
	} else if (activeFilter.value === 'inactive') {
		result = result.filter(coupon => coupon.status === 0);
	}
	
	return result;
});

// 页面加载时
onMounted(() => {
	loadCoupons();
});

// 加载优惠券数据
const loadCoupons = async () => {
	try {
		uni.showLoading({
			title: '加载中...'
		});

		const response = await getCouponList({
			merchant_id: 123, // 从存储中获取商家ID
			page: 1,
			limit: 100
		});

		uni.hideLoading();

		if (response && response.data) {
			coupons.value = response.data.list;
		}
	} catch (error) {
		uni.hideLoading();
		console.warn('API获取失败，使用模拟数据', error);
	}
};

// 搜索优惠券
const searchCoupons = () => {
	loadCoupons();
};

// 过滤优惠券
const filterCoupons = (type) => {
	activeFilter.value = type;
	loadCoupons();
};

// 导航到指定页面
const navigateTo = (url) => {
	uni.navigateTo({
		url: url
	});
};

// 获取类型文本
const getTypeText = (type) => {
	const typeMap = {
		1: '满减券',
		2: '折扣券',
		3: '无门槛券'
	};
	return typeMap[type] || '未知';
};

// 格式化日期
const formatDate = (dateStr) => {
	if (!dateStr) return '';
	return dateStr.split(' ')[0];
};

// 切换优惠券状态
const toggleCouponStatus = async (coupon) => {
	const newStatus = coupon.status === 1 ? 0 : 1;
	const statusText = newStatus === 1 ? '启用' : '禁用';
	
	uni.showModal({
		title: '确认操作',
		content: `确定要${statusText}【${coupon.name}】吗？`,
		success: async (res) => {
			if (res.confirm) {
				try {
					uni.showLoading({
						title: '处理中...'
					});

					await toggleCouponStatus(coupon.id, newStatus);

					uni.hideLoading();
					
					// 更新本地数据
					const index = coupons.value.findIndex(c => c.id === coupon.id);
					if (index !== -1) {
						coupons.value[index].status = newStatus;
					}
					
					uni.showToast({
						title: `${statusText}成功`,
						icon: 'success'
					});
				} catch (error) {
					uni.hideLoading();
					console.warn('API操作失败，使用模拟成功', error);
					
					// 模拟成功
					const index = coupons.value.findIndex(c => c.id === coupon.id);
					if (index !== -1) {
						coupons.value[index].status = newStatus;
					}
					
					uni.showToast({
						title: `${statusText}成功（模拟）`,
						icon: 'success'
					});
				}
			}
		}
	});
};
</script>

<style lang="scss">
.coupon-container {
	min-height: 100vh;
	background-color: #F5F5F5;
	padding-bottom: 30rpx;
}

.search-bar {
	padding: 20rpx;
	background-color: #FFFFFF;
	display: flex;
	align-items: center;
}

.search-input-box {
	flex: 1;
	height: 72rpx;
	background-color: #F5F5F5;
	border-radius: 36rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	margin-right: 20rpx;
}

.search-input {
	flex: 1;
	height: 72rpx;
	font-size: 28rpx;
	margin-left: 10rpx;
}

.add-btn {
	width: 160rpx;
	height: 72rpx;
	background-color: #007AFF;
	color: #FFFFFF;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 36rpx;
	padding: 0;
}

.filter-bar {
	display: flex;
	background-color: #FFFFFF;
	padding: 0 20rpx;
	margin-bottom: 20rpx;
	border-bottom: 1rpx solid #EEEEEE;
}

.filter-item {
	padding: 20rpx 30rpx;
	font-size: 28rpx;
	color: #666666;
	position: relative;
}

.filter-item.active {
	color: #007AFF;
	font-weight: bold;
	
	&::after {
		content: '';
		position: absolute;
		left: 30rpx;
		right: 30rpx;
		bottom: 0;
		height: 4rpx;
		background-color: #007AFF;
		border-radius: 2rpx;
	}
}

.coupon-list {
	padding: 0 20rpx;
}

.coupon-item {
	margin-bottom: 20rpx;
}

.coupon-card {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.card-inactive {
	opacity: 0.6;
}

.coupon-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.coupon-type {
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 6rpx;
	color: #FFFFFF;
}

.type-1 {
	background-color: #FF6B6B;
}

.type-2 {
	background-color: #4ECDC4;
}

.type-3 {
	background-color: #45B7D1;
}

.coupon-status {
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 6rpx;
}

.status-active {
	background-color: #EBFFEC;
	color: #00CC66;
}

.status-inactive {
	background-color: #FFF3E0;
	color: #FF9500;
}

.coupon-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 8rpx;
}

.coupon-desc {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 15rpx;
}

.coupon-stats {
	display: flex;
	justify-content: space-between;
	margin-bottom: 15rpx;
}

.stat-item {
	text-align: center;
}

.stat-label {
	font-size: 24rpx;
	color: #999999;
	display: block;
}

.stat-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #333333;
	display: block;
	margin-top: 4rpx;
}

.coupon-time {
	font-size: 24rpx;
	color: #666666;
	margin-bottom: 15rpx;
}

.time-label {
	color: #999999;
}

.coupon-actions {
	display: flex;
	justify-content: flex-end;
}

.action-btn {
	display: flex;
	align-items: center;
	padding: 8rpx 16rpx;
	margin-left: 20rpx;
	border-radius: 6rpx;
	background-color: #F5F5F5;
}

.action-text {
	font-size: 24rpx;
	color: #666666;
	margin-left: 6rpx;
}

.empty-tip {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 100rpx 0;
}

.empty-icon {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999999;
}
</style>
