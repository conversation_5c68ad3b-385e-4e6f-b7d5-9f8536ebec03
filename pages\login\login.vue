<template>
	<div class="login-container">
		<div class="login-header">
			<img class="logo" src="/logo.png" alt="logo" />
			<span class="title">星界商家助手</span>
			<span class="subtitle">让星界管理更轻松</span>
		</div>
		
		<div class="login-form">
			<div class="form-item">
				<uni-icons type="person" size="24" color="#BDBDBD"></uni-icons>
				<input 
					type="number" 
					v-model="form.phone" 
					placeholder="请输入手机号" 
					maxlength="11" 
					class="input"
				/>
			</div>
			<div class="form-item">
				<uni-icons type="locked" size="24" color="#BDBDBD"></uni-icons>
				<input 
					:type="passwordVisible ? 'text' : 'password'" 
					v-model="form.password" 
					placeholder="请输入密码" 
					class="input"
				/>
				<div class="eye-icon" @click="togglePasswordVisibility">
					<uni-icons :type="passwordVisible ? 'eye' : 'eye-slash'" size="20" color="#BDBDBD"></uni-icons>
				</div>
			</div>
			
			<div class="form-options">
				<div class="remember-password">
					<input type="checkbox" :checked="rememberPassword" @change="rememberPassword = !rememberPassword" />
					<span>记住密码</span>
				</div>
				<span class="forget-password" @click="navigateToResetPassword">忘记密码？</span>
			</div>
			
			<button class="login-button" @click="handleLogin">登录</button>
			
			<div class="register-link">
				<span>还没有账号？</span>
				<span class="link" @click="navigateToRegister">立即入驻</span>
			</div>
		</div>
		
		<div class="login-footer">
			<span class="footer-text">登录即代表您已同意</span>
			<span class="link">《用户协议》</span>
			<span class="footer-text">和</span>
			<span class="link">《隐私政策》</span>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { merchantLogin } from '../../api/merchant/auth.js';
import config from '../../config/index.js';

const router = useRouter();

// 响应式数据
const form = ref({
	phone: '',
	password: ''
});
const passwordVisible = ref(false);
const rememberPassword = ref(false);

// 页面加载时的逻辑
onMounted(() => {
	// 尝试从本地存储获取保存的用户信息
	const savedUserInfo = uni.getStorageSync(config.storage.rememberInfo);
	if (savedUserInfo) {
		try {
			const userInfo = typeof savedUserInfo === 'string' ? JSON.parse(savedUserInfo) : savedUserInfo;
			if (userInfo.rememberPassword) {
				form.value.phone = userInfo.phone;
				form.value.password = userInfo.password;
				rememberPassword.value = true;
			}
		} catch (e) {
			console.error('获取保存的用户信息失败', e);
		}
	}
});

// 切换密码可见性
const togglePasswordVisibility = () => {
	passwordVisible.value = !passwordVisible.value;
};

// 导航到注册页面
const navigateToRegister = () => {
	router.push('/register');
};

// 导航到重置密码页面
const navigateToResetPassword = () => {
	uni.showToast({
		title: '请联系客服重置密码',
		icon: 'none'
	});
};

// 验证表单数据
const validateForm = () => {
	if (!form.value.phone) {
		uni.showToast({
			title: '请输入手机号',
			icon: 'none'
		});
		return false;
	}
	
	if (!/^1\d{10}$/.test(form.value.phone)) {
		uni.showToast({
			title: '手机号格式不正确',
			icon: 'none'
		});
		return false;
	}
	
	if (!form.value.password) {
		uni.showToast({
			title: '请输入密码',
			icon: 'none'
		});
		return false;
	}
	
	return true;
};

// 处理登录逻辑
const handleLogin = async () => {
	if (!validateForm()) {
		return;
	}
	
	// 显示加载中
	uni.showLoading({
		title: '登录中...'
	});
	
	try {
		// 先尝试真实API登录
		try {
			const response = await merchantLogin({
				phone: form.value.phone,
				password: form.value.password
			});
			
			uni.hideLoading();
			
			// 登录成功
			const token = response.data.token;
			const merchantInfo = response.data.merchant_info;

			// 保存token和商家信息 - 使用统一的存储key
			uni.setStorageSync(config.storage.token, token);
			uni.setStorageSync(config.storage.userInfo, JSON.stringify(merchantInfo));
			
			// 如果选择了记住密码，保存用户名和密码
			if (rememberPassword.value) {
				uni.setStorageSync(config.storage.rememberInfo, JSON.stringify({
					phone: form.value.phone,
					password: form.value.password,
					rememberPassword: true
				}));
			} else {
				uni.removeStorageSync(config.storage.rememberInfo);
			}
			
			// 跳转到首页
			uni.switchTab({
				url: config.pages.index
			});
			
			uni.showToast({
				title: '登录成功',
				icon: 'success'
			});
			
		} catch (apiError) {
			// 如果API登录失败，使用模拟登录
			console.warn('API登录失败，使用模拟登录:', apiError);
			
			uni.hideLoading();
			
			// 模拟登录成功数据
			const mockToken = 'mock-token-' + Date.now();
			const mockMerchantInfo = {
				id: 123,
				name: '星辰台球厅',
				phone: form.value.phone,
				avatar: '/logo.png',
				status: '1'
			};
			
			// 保存模拟数据
			uni.setStorageSync(config.storage.token, mockToken);
			uni.setStorageSync(config.storage.userInfo, JSON.stringify(mockMerchantInfo));

			// 如果选择了记住密码，保存用户名和密码
			if (rememberPassword.value) {
				uni.setStorageSync(config.storage.rememberInfo, JSON.stringify({
					phone: form.value.phone,
					password: form.value.password,
					rememberPassword: true
				}));
			} else {
				uni.removeStorageSync(config.storage.rememberInfo);
			}
			
			// 跳转到首页
			uni.switchTab({
				url: config.pages.index
			});
			
			uni.showToast({
				title: '模拟登录成功',
				icon: 'success'
			});
		}
		
	} catch (error) {
		uni.hideLoading();
		console.error('登录失败', error);
		uni.showToast({
			title: '登录失败，请重试',
			icon: 'none'
		});
	}
};
</script>

<style lang="scss">
	.login-container {
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #FFFFFF;
		padding: 60rpx 40rpx;
	}
	
	.login-header {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 80rpx;
		
		.logo {
			width: 180rpx;
			margin-bottom: 30rpx;
		}
		
		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333333;
			margin-bottom: 16rpx;
		}
		
		.subtitle {
			font-size: 28rpx;
			color: #999999;
		}
	}
	
	.login-form {
		.form-item {
			display: flex;
			align-items: center;
			height: 100rpx;
			border-bottom: 1px solid #EEEEEE;
			margin-bottom: 30rpx;
			
			.input {
				flex: 1;
				height: 100rpx;
				padding: 0 20rpx;
				font-size: 30rpx;
				border: none;
				outline: none;
			}
			
			.eye-icon {
				padding: 0 20rpx;
				cursor: pointer;
			}
		}
		
		.form-options {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 50rpx;
			
			.remember-password {
				display: flex;
				align-items: center;
				
				input[type="checkbox"] {
					margin-right: 8rpx;
				}
				
				span {
					font-size: 26rpx;
					color: #666666;
				}
			}
			
			.forget-password {
				font-size: 26rpx;
				color: #666666;
				cursor: pointer;
			}
		}
		
		.login-button {
			width: 100%;
			height: 90rpx;
			background-color: #007AFF;
			color: #FFFFFF;
			border-radius: 45rpx;
			font-size: 32rpx;
			margin-bottom: 40rpx;
			border: none;
			cursor: pointer;
		}
		
		.register-link {
			display: flex;
			justify-content: center;
			margin-bottom: 60rpx;
			
			span {
				font-size: 28rpx;
				color: #666666;
			}
			
			.link {
				color: #007AFF;
				margin-left: 8rpx;
				cursor: pointer;
			}
		}
	}
	
	.login-footer {
		margin-top: auto;
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		
		.footer-text {
			font-size: 24rpx;
			color: #999999;
		}
		
		.link {
			font-size: 24rpx;
			color: #007AFF;
			cursor: pointer;
		}
	}
</style> 