<template>
	<view class="register-container">
		<view class="register-header">
			<text class="title">商家入驻</text>
			<text class="subtitle">填写信息，立即开启您的星界业务</text>
		</view>
		
		<view class="register-form">
			<view class="form-section">
				<view class="section-title">基本信息</view>
				
				<!-- 店铺名称 -->
				<view class="form-item">
					<text class="form-label">店铺名称<text class="required">*</text></text>
					<view class="input-box">
						<input 
							type="text" 
							v-model="form.shopName" 
							placeholder="请输入星界名称" 
							maxlength="30"
						/>
					</view>
				</view>
				
				<!-- 联系人 -->
				<view class="form-item">
					<text class="form-label">联系人<text class="required">*</text></text>
					<view class="input-box">
						<input 
							type="text" 
							v-model="form.contactName" 
							placeholder="请输入联系人姓名" 
							maxlength="20"
						/>
					</view>
				</view>
				
				<!-- 手机号码 -->
				<view class="form-item">
					<text class="form-label">手机号码<text class="required">*</text></text>
					<view class="input-box">
						<input 
							type="number" 
							v-model="form.contactPhone" 
							placeholder="请输入手机号码" 
							maxlength="11"
						/>
					</view>
				</view>
				
				<!-- 设置密码 -->
				<view class="form-item">
					<text class="form-label">设置密码<text class="required">*</text></text>
					<view class="input-box password-box">
						<input 
							:type="passwordVisible ? 'text' : 'password'" 
							v-model="form.password" 
							placeholder="请设置登录密码" 
							maxlength="20"
						/>
						<view class="eye-icon" @click="togglePasswordVisibility">
							<uni-icons :type="passwordVisible ? 'eye' : 'eye-slash'" size="20" color="#BDBDBD"></uni-icons>
						</view>
					</view>
					<text class="form-tip">密码长度为8-20位，必须包含字母和数字</text>
				</view>
				
				<!-- 短信验证码 -->
				<view class="form-item">
					<text class="form-label">短信验证码<text class="required">*</text></text>
					<view class="input-box code-box">
						<input 
							type="number" 
							v-model="form.verifyCode" 
							placeholder="请输入验证码" 
							maxlength="6"
						/>
						<view class="code-button" @click="sendVerifyCode" :class="{ disabled: countdown > 0 }">
							<text v-if="countdown > 0">{{countdown}}秒后重新获取</text>
							<text v-else>获取验证码</text>
						</view>
					</view>
				</view>
			</view>
			
			<view class="form-section">
				<view class="section-title">店铺信息</view>
				
				<!-- 店铺地址 -->
				<view class="form-item">
					<text class="form-label">店铺地址<text class="required">*</text></text>
					<view class="address-picker" @click="selectLocation">
						<text v-if="form.address">{{form.address}}</text>
						<text class="placeholder" v-else>点击选择店铺地址</text>
						<uni-icons type="right" size="16" color="#BDBDBD"></uni-icons>
					</view>
				</view>
				
				<!-- 详细地址 -->
				<view class="form-item">
					<text class="form-label">详细地址<text class="required">*</text></text>
					<view class="input-box">
						<input 
							type="text" 
							v-model="form.addressDetail" 
							placeholder="请输入详细地址（如楼层、门牌号等）" 
							maxlength="50"
						/>
					</view>
				</view>
				
				<!-- 营业执照 -->
				<view class="form-item">
					<text class="form-label">营业执照<text class="required">*</text></text>
					<view class="upload-box" @click="uploadLicense">
						<image v-if="form.licenseImage" :src="form.licenseImage" mode="aspectFit" class="uploaded-image"></image>
						<view v-else class="upload-placeholder">
							<uni-icons type="camera" size="24" color="#BDBDBD"></uni-icons>
							<text>点击上传营业执照</text>
						</view>
					</view>
					<text class="form-tip">请上传营业执照正本或副本照片，确保照片清晰可见</text>
				</view>
				
				<!-- 台球桌数量 -->
				<view class="form-item">
					<text class="form-label">台球桌数量<text class="required">*</text></text>
					<view class="input-box">
						<input 
							type="number" 
							v-model="form.tableCount" 
							placeholder="请输入台球桌数量" 
						/>
					</view>
				</view>
				
				<!-- 店铺简介 -->
				<view class="form-item">
					<text class="form-label">店铺简介</text>
					<view class="textarea-box">
						<textarea 
							v-model="form.description" 
							placeholder="请简要描述您的星界特色、设施等信息" 
							maxlength="200"
						/>
						<text class="word-count">{{form.description.length}}/200</text>
					</view>
				</view>
			</view>
			
			<view class="agreement">
				<checkbox :checked="agreement" @click="agreement = !agreement" color="#007AFF" />
				<text class="agreement-text">我已阅读并同意</text>
				<text class="agreement-link">《商家入驻协议》</text>
				<text class="agreement-text">和</text>
				<text class="agreement-link">《隐私政策》</text>
			</view>
			
			<button class="submit-button" @click="handleSubmit" :disabled="!agreement">提交申请</button>
		</view>
	</view>
</template>

<script setup>
import { ref, onUnmounted } from 'vue';
import { merchantRegister } from '../../api/merchant/auth.js';

// 配置信息
const config = {
	storage: {
		token: 'merchant_token',
		userInfo: 'merchant_info',
		rememberInfo: 'merchantUserInfo'
	},
	pages: {
		login: '/pages/login/login',
		index: '/pages/index/index'
	}
};

// 响应式数据
const form = ref({
	shopName: '',
	contactName: '',
	contactPhone: '',
	password: '',
	verifyCode: '',
	address: '',
	addressDetail: '',
	licenseImage: '',
	tableCount: '',
	description: ''
});

const passwordVisible = ref(false);
const agreement = ref(false);
const countdown = ref(0);
const timer = ref(null);

// 页面卸载时清除定时器
onUnmounted(() => {
	if (timer.value) {
		clearInterval(timer.value);
	}
});

// 切换密码可见性
const togglePasswordVisibility = () => {
	passwordVisible.value = !passwordVisible.value;
};

// 发送验证码
const sendVerifyCode = () => {
	if (countdown.value > 0) {
		return;
	}
	
	// 验证手机号
	if (!form.value.contactPhone) {
		uni.showToast({
			title: '请先输入手机号',
			icon: 'none'
		});
		return;
	}
	
	if (!/^1\d{10}$/.test(form.value.contactPhone)) {
		uni.showToast({
			title: '手机号格式不正确',
			icon: 'none'
		});
		return;
	}
	
	// 显示加载中
	uni.showLoading({
		title: '发送中...'
	});
	
	// 调用发送验证码接口
	uni.request({
		url: 'http://localhost:8080/merchant/sendSms',
		method: 'POST',
		data: {
			phone: form.value.contactPhone
		},
		success: (res) => {
			uni.hideLoading();
			
			if (res.data.code === 200) {
				// 发送成功，开始倒计时
				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				});
				
				startCountdown();
			} else {
				// 发送失败
				uni.showToast({
					title: res.data.msg || '验证码发送失败',
					icon: 'none'
				});
			}
		},
		fail: (err) => {
			uni.hideLoading();
			
			uni.showToast({
				title: '网络请求失败，请检查网络连接',
				icon: 'none'
			});
			
			console.error('发送验证码请求失败', err);
		}
	});
};

// 开始倒计时
const startCountdown = () => {
	countdown.value = 60;
	
	timer.value = setInterval(() => {
		if (countdown.value > 0) {
			countdown.value--;
		} else {
			clearInterval(timer.value);
			timer.value = null;
		}
	}, 1000);
};

// 选择地址
const selectLocation = () => {
	uni.chooseLocation({
		success: (res) => {
			form.value.address = res.address;
			// 可以额外保存经纬度信息
			form.value.latitude = res.latitude;
			form.value.longitude = res.longitude;
		}
	});
};

// 上传营业执照
const uploadLicense = () => {
	uni.chooseImage({
		count: 1,
		sourceType: ['album', 'camera'],
		success: (res) => {
			const tempFilePath = res.tempFilePaths[0];
			
			// 显示加载中
			uni.showLoading({
				title: '上传中...'
			});
			
			// 模拟上传过程，实际项目中应该调用上传接口
			setTimeout(() => {
				uni.hideLoading();
				
				// 更新表单数据
				form.value.licenseImage = tempFilePath;
				
				uni.showToast({
					title: '上传成功',
					icon: 'success'
				});
			}, 1000);
		}
	});
};

// 验证表单数据
const validateForm = () => {
	if (!form.value.shopName) {
		uni.showToast({
			title: '请输入店铺名称',
			icon: 'none'
		});
		return false;
	}
	
	if (!form.value.contactName) {
		uni.showToast({
			title: '请输入联系人姓名',
			icon: 'none'
		});
		return false;
	}
	
	if (!form.value.contactPhone) {
		uni.showToast({
			title: '请输入手机号码',
			icon: 'none'
		});
		return false;
	}
	
	if (!/^1\d{10}$/.test(form.value.contactPhone)) {
		uni.showToast({
			title: '手机号格式不正确',
			icon: 'none'
		});
		return false;
	}
	
	if (!form.value.password) {
		uni.showToast({
			title: '请设置密码',
			icon: 'none'
		});
		return false;
	}
	
	// 密码强度验证：至少8位，包含字母和数字
	if (!/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,20}$/.test(form.value.password)) {
		uni.showToast({
			title: '密码必须为8-20位且包含字母和数字',
			icon: 'none'
		});
		return false;
	}
	
	if (!form.value.verifyCode) {
		uni.showToast({
			title: '请输入验证码',
			icon: 'none'
		});
		return false;
	}
	
	if (!form.value.address) {
		uni.showToast({
			title: '请选择店铺地址',
			icon: 'none'
		});
		return false;
	}
	
	if (!form.value.addressDetail) {
		uni.showToast({
			title: '请输入详细地址',
			icon: 'none'
		});
		return false;
	}
	
	if (!form.value.licenseImage) {
		uni.showToast({
			title: '请上传营业执照',
			icon: 'none'
		});
		return false;
	}
	
	if (!form.value.tableCount) {
		uni.showToast({
			title: '请输入台球桌数量',
			icon: 'none'
		});
		return false;
	}
	
	return true;
};

// 提交注册
const handleSubmit = async () => {
	if (!agreement.value) {
		uni.showToast({
			title: '请先阅读并同意协议',
			icon: 'none'
		});
		return;
	}

	if (!validateForm()) {
		return;
	}

	// 显示加载中
	uni.showLoading({
		title: '提交中...'
	});

	try {
		// 调用注册接口
		const response = await merchantRegister({
			name: form.value.shopName,
			contact_person: form.value.contactName,
			contact_phone: form.value.contactPhone,
			password: form.value.password,
			address: form.value.address + ' ' + form.value.addressDetail,
			description: form.value.description
		});

		uni.hideLoading();

		// 检查响应码
		if (response.code === '0000') {
			// 注册成功
			uni.showModal({
				title: '提交成功',
				content: '您的入驻申请已提交，我们将在1-3个工作日内审核，请耐心等待',
				showCancel: false,
				success: () => {
					// 跳转到登录页
					uni.navigateBack();
				}
			});
		} else {
			// 注册失败
			uni.showToast({
				title: response.message || '提交失败，请重试',
				icon: 'none'
			});
		}

	} catch (error) {
		uni.hideLoading();
		console.error('注册请求失败', error);

		// 如果API失败，显示模拟成功
		uni.showModal({
			title: '提交成功',
			content: '您的入驻申请已提交（模拟），我们将在1-3个工作日内审核，请耐心等待',
			showCancel: false,
			success: () => {
				// 跳转到登录页
				uni.navigateBack();
			}
		});
	}
};
</script>

<style lang="scss">
	.register-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 40rpx;
	}
	
	.register-header {
		padding: 40rpx 30rpx;
		background-color: #FFFFFF;
		border-bottom: 1rpx solid #EEEEEE;
		
		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333333;
			margin-bottom: 16rpx;
			display: block;
		}
		
		.subtitle {
			font-size: 28rpx;
			color: #999999;
		}
	}
	
	.register-form {
		padding: 30rpx;
	}
	
	.form-section {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		
		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333333;
			margin-bottom: 30rpx;
			position: relative;
			padding-left: 20rpx;
			
			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 6rpx;
				bottom: 6rpx;
				width: 6rpx;
				background-color: #007AFF;
				border-radius: 3rpx;
			}
		}
	}
	
	.form-item {
		margin-bottom: 30rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.form-label {
			font-size: 28rpx;
			color: #333333;
			margin-bottom: 15rpx;
			display: block;
			
			.required {
				color: #FF3B30;
				margin-left: 4rpx;
			}
		}
		
		.input-box {
			background-color: #F5F5F5;
			border-radius: 8rpx;
			height: 90rpx;
			padding: 0 20rpx;
			display: flex;
			align-items: center;
			
			input {
				flex: 1;
				height: 90rpx;
				font-size: 28rpx;
				color: #333333;
			}
		}
		
		.password-box {
			.eye-icon {
				padding: 0 10rpx;
			}
		}
		
		.code-box {
			.code-button {
				height: 70rpx;
				background-color: #007AFF;
				color: #FFFFFF;
				border-radius: 8rpx;
				font-size: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0 20rpx;
				white-space: nowrap;
				
				&.disabled {
					background-color: #CCCCCC;
				}
			}
		}
		
		.address-picker {
			background-color: #F5F5F5;
			border-radius: 8rpx;
			height: 90rpx;
			padding: 0 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 28rpx;
			color: #333333;
			
			.placeholder {
				color: #BDBDBD;
			}
		}
		
		.upload-box {
			width: 100%;
			height: 400rpx;
			background-color: #F5F5F5;
			border-radius: 8rpx;
			overflow: hidden;
			position: relative;
			
			.uploaded-image {
				width: 100%;
				height: 100%;
			}
			
			.upload-placeholder {
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				
				text {
					font-size: 28rpx;
					color: #BDBDBD;
					margin-top: 20rpx;
				}
			}
		}
		
		.textarea-box {
			background-color: #F5F5F5;
			border-radius: 8rpx;
			padding: 20rpx;
			position: relative;
			
			textarea {
				width: 100%;
				height: 200rpx;
				font-size: 28rpx;
				color: #333333;
			}
			
			.word-count {
				position: absolute;
				right: 20rpx;
				bottom: 20rpx;
				font-size: 24rpx;
				color: #BDBDBD;
			}
		}
		
		.form-tip {
			font-size: 24rpx;
			color: #999999;
			margin-top: 10rpx;
			display: block;
		}
	}
	
	.agreement {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin: 20rpx 0 40rpx;
		
		.agreement-text {
			font-size: 26rpx;
			color: #666666;
			margin-left: 8rpx;
		}
		
		.agreement-link {
			font-size: 26rpx;
			color: #007AFF;
		}
	}
	
	.submit-button {
		width: 100%;
		height: 90rpx;
		background-color: #007AFF;
		color: #FFFFFF;
		border-radius: 45rpx;
		font-size: 32rpx;
		margin-bottom: 40rpx;
		
		&[disabled] {
			background-color: #CCCCCC;
		}
	}
</style> 