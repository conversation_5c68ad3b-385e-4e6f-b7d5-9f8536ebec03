// 通用HTTP请求工具
import config from '../config/index.js';

// API基础地址，从全局配置获取
const baseURL = config.api.baseURL;

// 请求拦截器 - 添加token等
function addRequestInterceptor(requestConfig) {
  // 从本地存储获取token
  const token = uni.getStorageSync(config.storage.token);
  if (token) {
    requestConfig.header = requestConfig.header || {};
    requestConfig.header['Authorization'] = `Bearer ${token}`;
  }

  // 设置默认Content-Type
  requestConfig.header = requestConfig.header || {};
  requestConfig.header['Content-Type'] = 'application/json';

  return requestConfig;
}

// 响应拦截器 - 统一处理响应
function handleResponse(response) {
  const { data, statusCode } = response;
  
  // HTTP状态码检查
  if (statusCode !== 200) {
    uni.showToast({
      title: '网络请求失败',
      icon: 'none'
    });
    return Promise.reject(new Error('网络请求失败'));
  }
  
  // 业务状态码检查
  if (data.code === '0000') {
    return data;
  } else {
    // 处理业务错误
    const errorMessage = data.message || '请求失败';
    uni.showToast({
      title: errorMessage,
      icon: 'none'
    });
    
    // token过期，跳转到登录页
    if (data.code === '1003') {
      uni.removeStorageSync(config.storage.token);
      uni.removeStorageSync(config.storage.userInfo);
      uni.reLaunch({
        url: config.pages.login
      });
    }
    
    return Promise.reject(new Error(errorMessage));
  }
}

// 通用请求方法
function request(options) {
  return new Promise((resolve, reject) => {
    // 添加baseURL
    const url = options.url.startsWith('http') ? options.url : baseURL + options.url;
    
    // 配置请求参数
    const config = {
      url,
      method: options.method || 'GET',
      data: options.data || {},
      header: options.header || {},
      timeout: options.timeout || 10000
    };
    
    // 应用请求拦截器
    const interceptedConfig = addRequestInterceptor(config);
    
    // 发起请求
    uni.request({
      ...interceptedConfig,
      success: (response) => {
        try {
          const result = handleResponse(response);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      },
      fail: (error) => {
        console.error('请求失败:', error);
        uni.showToast({
          title: '网络连接失败',
          icon: 'none'
        });
        reject(error);
      }
    });
  });
}

// GET请求
function get(url, params = {}) {
  const queryString = Object.keys(params).length > 0 
    ? '?' + Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
    : '';
  
  return request({
    url: url + queryString,
    method: 'GET'
  });
}

// POST请求
function post(url, data = {}) {
  return request({
    url,
    method: 'POST',
    data
  });
}

// PUT请求
function put(url, data = {}) {
  return request({
    url,
    method: 'PUT',
    data
  });
}

// DELETE请求
function del(url, data = {}) {
  return request({
    url,
    method: 'DELETE',
    data
  });
}

export default {
  get,
  post,
  put,
  delete: del,
  request
}; 