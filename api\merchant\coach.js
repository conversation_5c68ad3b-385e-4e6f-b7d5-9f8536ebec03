import request from '../../utils/request.js';

// 教练管理相关API

/**
 * 获取教练列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} params.status 教练状态
 * @param {string} params.skill_level 技能等级
 */
export const getCoachList = (params = {}) => {
  return request.get('/merchant/coach/list', params);
};

/**
 * 获取教练详情
 * @param {number} coachId 教练ID
 */
export const getCoachDetail = (coachId) => {
  return request.get(`/merchant/coach/detail/${coachId}`);
};

/**
 * 添加教练
 * @param {Object} data 教练信息
 * @param {string} data.name 教练姓名
 * @param {string} data.phone 手机号
 * @param {string} data.skill_level 技能等级
 * @param {number} data.hourly_rate 小时费率
 * @param {string} data.description 个人简介
 * @param {string} data.avatar 头像
 */
export const addCoach = (data) => {
  return request.post('/merchant/coach/add', data);
};

/**
 * 更新教练信息
 * @param {number} coachId 教练ID
 * @param {Object} data 更新数据
 */
export const updateCoach = (coachId, data) => {
  return request.put(`/merchant/coach/update/${coachId}`, data);
};

/**
 * 删除教练
 * @param {number} coachId 教练ID
 */
export const deleteCoach = (coachId) => {
  return request.delete(`/merchant/coach/delete/${coachId}`);
};

/**
 * 设置教练状态
 * @param {number} coachId 教练ID
 * @param {string} status 状态 (active/inactive/suspended)
 */
export const setCoachStatus = (coachId, status) => {
  return request.post(`/merchant/coach/status/${coachId}`, { status });
};

/**
 * 获取教练排班表
 * @param {number} coachId 教练ID
 * @param {Object} params 查询参数
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 */
export const getCoachSchedule = (coachId, params = {}) => {
  return request.get(`/merchant/coach/schedule/${coachId}`, params);
};

/**
 * 设置教练排班
 * @param {number} coachId 教练ID
 * @param {Object} data 排班数据
 * @param {string} data.date 日期
 * @param {Array} data.time_slots 时间段
 */
export const setCoachSchedule = (coachId, data) => {
  return request.post(`/merchant/coach/schedule/${coachId}`, data);
};

/**
 * 获取教练收益统计
 * @param {number} coachId 教练ID
 * @param {Object} params 查询参数
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 */
export const getCoachEarnings = (coachId, params = {}) => {
  return request.get(`/merchant/coach/earnings/${coachId}`, params);
}; 