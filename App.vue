<script>
export default {
	onLaunch: function() {
		console.log('星界商家端应用启动')

		// 检查登录状态
		const token = uni.getStorageSync('merchant_token')
		if (!token) {
			// 没有登录，跳转到登录页
			uni.reLaunch({
				url: '/pages/login/login'
			})
		}
	},
	onShow: function() {
		console.log('App Show')
	},
	onHide: function() {
		console.log('App Hide')
	}
}
</script>

<style>
	/*每个页面公共css */
	#app {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		height: 100vh;
	}

	/* 全局样式重置 */
	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	body {
		background-color: #f5f5f5;
	}
</style>
