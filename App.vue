<template>
	<div id="app">
		<router-view />
	</div>
</template>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
	console.log('星界商家端应用启动')
})
</script>

<style>
	/*每个页面公共css */
	#app {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		height: 100vh;
	}

	/* 全局样式重置 */
	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	body {
		background-color: #f5f5f5;
	}
</style>
