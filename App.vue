<template>
	<view>
		<!-- uniapp App.vue 根组件 -->
	</view>
</template>

<script>
export default {
	onLaunch: function() {
		console.log('星界商家端应用启动')
		// 应用初始化
	},
	onShow: function() {
		console.log('App Show')
	},
	onHide: function() {
		console.log('App Hide')
	}
}
</script>

<style>
	/*每个页面公共css */
	page {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;
		background-color: #f5f5f5;
	}

	/* 全局样式重置 */
	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}
	
	/* 全局通用样式 */
	.container {
		padding: 20rpx;
	}
	
	.text-center {
		text-align: center;
	}
	
	.flex {
		display: flex;
	}
	
	.flex-center {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.btn-primary {
		background-color: #007AFF;
		color: white;
		border-radius: 8rpx;
		padding: 20rpx 40rpx;
		border: none;
	}
</style>
