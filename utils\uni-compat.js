// 创建一个全局的 uni 对象来兼容 uni-app API

const uni = {
  // 导航相关
  navigateTo(options) {
    // 简单的页面跳转，使用hash路由
    const url = options.url
    window.location.hash = `#${url}`

    if (options.success) {
      options.success()
    }
  },

  switchTab(options) {
    // 切换tabBar页面
    const url = options.url
    window.location.hash = `#${url}`

    if (options.success) {
      options.success()
    }
  },

  reLaunch(options) {
    // 重新启动到指定页面
    const url = options.url
    window.location.hash = `#${url}`

    if (options.success) {
      options.success()
    }
  },

  navigateBack(options = {}) {
    // 返回上一页
    window.history.back()

    if (options.success) {
      options.success()
    }
  },

  // 存储相关
  setStorageSync(key, data) {
    try {
      const value = typeof data === 'object' ? JSON.stringify(data) : data
      localStorage.setItem(key, value)
    } catch (e) {
      console.error('setStorageSync error:', e)
    }
  },

  getStorageSync(key) {
    try {
      const value = localStorage.getItem(key)
      if (value === null) return ''
      
      // 尝试解析为 JSON，如果失败则返回原始字符串
      try {
        return JSON.parse(value)
      } catch {
        return value
      }
    } catch (e) {
      console.error('getStorageSync error:', e)
      return ''
    }
  },

  removeStorageSync(key) {
    try {
      localStorage.removeItem(key)
    } catch (e) {
      console.error('removeStorageSync error:', e)
    }
  },

  // 提示相关
  showToast(options) {
    const message = options.title || options.message || '操作完成'
    const icon = options.icon || 'none'
    
    // 创建简单的 toast 提示
    const toast = document.createElement('div')
    toast.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      font-size: 14px;
      z-index: 9999;
      max-width: 300px;
      text-align: center;
    `
    toast.textContent = message
    document.body.appendChild(toast)
    
    setTimeout(() => {
      if (document.body.contains(toast)) {
        document.body.removeChild(toast)
      }
    }, options.duration || 2000)
  },

  showLoading(options = {}) {
    const title = options.title || '加载中...'
    
    // 移除已存在的 loading
    const existing = document.getElementById('uni-loading')
    if (existing) {
      existing.remove()
    }
    
    const loading = document.createElement('div')
    loading.id = 'uni-loading'
    loading.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9998;
    `
    
    const content = document.createElement('div')
    content.style.cssText = `
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 20px;
      border-radius: 6px;
      text-align: center;
      min-width: 120px;
    `
    
    const spinner = document.createElement('div')
    spinner.style.cssText = `
      width: 20px;
      height: 20px;
      border: 2px solid #ffffff30;
      border-top: 2px solid #ffffff;
      border-radius: 50%;
      margin: 0 auto 10px;
      animation: spin 1s linear infinite;
    `
    
    const text = document.createElement('div')
    text.textContent = title
    text.style.fontSize = '14px'
    
    content.appendChild(spinner)
    content.appendChild(text)
    loading.appendChild(content)
    
    // 添加旋转动画
    if (!document.getElementById('spin-style')) {
      const style = document.createElement('style')
      style.id = 'spin-style'
      style.textContent = `
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `
      document.head.appendChild(style)
    }
    
    document.body.appendChild(loading)
  },

  hideLoading() {
    const loading = document.getElementById('uni-loading')
    if (loading) {
      loading.remove()
    }
  },

  showModal(options) {
    return new Promise((resolve) => {
      const confirmed = window.confirm(options.content || '确定要执行此操作吗？')
      resolve({
        confirm: confirmed,
        cancel: !confirmed
      })
    })
  },

  // 网络请求 (可选，如果使用自定义的 request 工具)
  request(options) {
    return fetch(options.url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: options.data ? JSON.stringify(options.data) : undefined
    })
    .then(response => response.json())
    .then(data => {
      if (options.success) options.success({ data })
      return { data }
    })
    .catch(error => {
      if (options.fail) options.fail(error)
      throw error
    })
  },

  // 设备API
  makePhoneCall(options) {
    if (options.phoneNumber) {
      window.open(`tel:${options.phoneNumber}`)
      if (options.success) options.success()
    } else if (options.fail) {
      options.fail(new Error('电话号码不能为空'))
    }
  },

  chooseLocation(options) {
    // 简单的地址选择模拟
    const address = prompt('请输入地址:')
    if (address && options.success) {
      options.success({
        address: address,
        latitude: 0,
        longitude: 0
      })
    } else if (options.fail) {
      options.fail(new Error('用户取消选择'))
    }
  },

  chooseImage(options) {
    return new Promise((resolve, reject) => {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = 'image/*'
      input.multiple = (options.count || 1) > 1
      
      input.onchange = (event) => {
        const files = Array.from(event.target.files)
        const tempFilePaths = files.map(file => URL.createObjectURL(file))
        
        const result = {
          tempFilePaths: tempFilePaths,
          tempFiles: files
        }
        
        if (options.success) options.success(result)
        resolve(result)
      }
      
      input.onerror = (error) => {
        if (options.fail) options.fail(error)
        reject(error)
      }
      
      input.click()
    })
  }
}

// 导出 uni 对象
export default uni

// 也可以挂载到全局
if (typeof window !== 'undefined') {
  window.uni = uni
} 