// 全局配置文件
const config = {
  // API基础配置
  api: {
    // 开发环境API地址
    baseURL: 'http://localhost:8000/api',
    
    // 生产环境API地址（后续替换）
    // baseURL: 'https://your-production-domain.com/api',
    
    // 请求超时时间
    timeout: 10000,
    
    // 请求重试次数
    retryCount: 3
  },
  
  // 应用配置
  app: {
    name: '星界商家助手',
    version: '1.0.0',
    description: '台球预订系统商家端'
  },
  
  // 存储键名配置
  storage: {
    token: 'merchant_token',
    userInfo: 'merchant_info',
    rememberInfo: 'merchantUserInfo'
  },
  
  // 页面路径配置
  pages: {
    login: '/pages/login/login',
    index: '/pages/index/index',
    tabBar: [
      '/pages/index/index',
      '/pages/appointment/appointment', 
      '/pages/table/table',
      '/pages/coach/coach',
      '/pages/mine/mine'
    ]
  },
  
  // 业务配置
  business: {
    // 台桌类型
    tableTypes: [
      { value: 'snooker', label: '斯诺克' },
      { value: 'pool', label: '九球' },
      { value: 'carom', label: '开伦' }
    ],
    
    // 优惠券类型
    couponTypes: [
      { value: 1, label: '满减券' },
      { value: 2, label: '折扣券' },
      { value: 3, label: '无门槛券' }
    ],
    
    // 订单状态
    orderStatus: {
      pending: '待确认',
      confirmed: '已确认', 
      completed: '已完成',
      cancelled: '已取消'
    }
  }
};

export default config;
