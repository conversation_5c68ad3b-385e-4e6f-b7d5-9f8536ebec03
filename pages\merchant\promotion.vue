<template>
	<view class="promotion-container">
		<view class="tips-card">
			<uni-icons type="info" size="16" color="#007AFF"></uni-icons>
			<text class="tips-text">创建优惠活动，吸引更多顾客到店消费</text>
		</view>
		
		<!-- 优惠活动列表 -->
		<view class="promotion-list">
			<view class="empty-tip" v-if="promotionList.length === 0">
				<image class="empty-image" src="/static/images/empty.png" mode="widthFix"></image>
				<text class="empty-text">暂无优惠活动，点击下方按钮创建</text>
			</view>
			
			<view class="promotion-item" v-for="(item, index) in promotionList" :key="index">
				<view class="item-header">
					<view class="tag" :class="getStatusClass(item.status)">{{getStatusText(item.status)}}</view>
					<text class="item-title">{{item.name}}</text>
					<view class="edit-btn" @click="editPromotion(index)">
						<uni-icons type="compose" size="18" color="#007AFF"></uni-icons>
					</view>
				</view>
				
				<view class="item-content">
					<view class="info-row">
						<text class="info-label">活动时间：</text>
						<text class="info-value">{{item.startDate}} 至 {{item.endDate}}</text>
					</view>
					<view class="info-row">
						<text class="info-label">活动类型：</text>
						<text class="info-value">{{getTypeText(item.type)}}</text>
					</view>
					<view class="info-row">
						<text class="info-label">优惠内容：</text>
						<text class="info-value">{{getPromotionContent(item)}}</text>
					</view>
					<view class="info-row" v-if="item.description">
						<text class="info-label">活动说明：</text>
						<text class="info-value">{{item.description}}</text>
					</view>
				</view>
				
				<view class="item-footer">
					<view class="footer-btn delete-btn" @click="deletePromotion(index)">
						<uni-icons type="trash" size="14" color="#FF3B30"></uni-icons>
						<text>删除</text>
					</view>
					<view class="footer-btn" :class="item.status === 1 ? 'stop-btn' : 'start-btn'" @click="togglePromotionStatus(index)">
						<uni-icons :type="item.status === 1 ? 'closeempty' : 'refreshempty'" size="14" :color="item.status === 1 ? '#FF9500' : '#00CC66'"></uni-icons>
						<text>{{item.status === 1 ? '停止' : '启用'}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 创建优惠活动表单 -->
		<view class="form-panel" v-if="showForm">
			<view class="panel-header">
				<text class="panel-title">{{isEdit ? '编辑优惠活动' : '创建优惠活动'}}</text>
				<view class="close-btn" @click="cancelForm">
					<uni-icons type="close" size="20" color="#999999"></uni-icons>
				</view>
			</view>
			
			<view class="panel-content">
				<view class="form-item">
					<text class="form-label">活动名称</text>
					<view class="input-box">
						<input type="text" v-model="formData.name" placeholder="请输入活动名称"/>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">活动时间</text>
					<view class="date-picker-group">
						<view class="date-picker" @click="openDatePicker('start')">
							<text v-if="formData.startDate">{{formData.startDate}}</text>
							<text class="placeholder" v-else>开始日期</text>
							<uni-icons type="calendar" size="14" color="#999999"></uni-icons>
						</view>
						<text class="separator">至</text>
						<view class="date-picker" @click="openDatePicker('end')">
							<text v-if="formData.endDate">{{formData.endDate}}</text>
							<text class="placeholder" v-else>结束日期</text>
							<uni-icons type="calendar" size="14" color="#999999"></uni-icons>
						</view>
					</view>
				</view>
				
				<view class="form-item">
					<text class="form-label">活动类型</text>
					<view class="input-box">
						<picker @change="typeChange" :value="typeIndex" :range="typeOptions">
							<view class="picker-view">
								<text v-if="typeIndex !== ''">{{typeOptions[typeIndex]}}</text>
								<text v-else class="placeholder">请选择活动类型</text>
								<uni-icons type="bottom" size="14" color="#999999"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
				
				<!-- 折扣优惠设置 -->
				<view class="form-item" v-if="formData.type === 'discount'">
					<text class="form-label">折扣力度</text>
					<view class="input-box discount-input">
						<input type="digit" v-model="formData.discountValue" placeholder="请输入折扣"/>
						<text class="unit">折</text>
					</view>
				</view>
				
				<!-- 满减优惠设置 -->
				<view v-if="formData.type === 'fullReduction'">
					<view class="form-item">
						<text class="form-label">满额条件</text>
						<view class="input-box price-input">
							<text class="prefix">满</text>
							<input type="digit" v-model="formData.fullAmount" placeholder="请输入满额金额"/>
							<text class="unit">元</text>
						</view>
					</view>
					<view class="form-item">
						<text class="form-label">减免金额</text>
						<view class="input-box price-input">
							<text class="prefix">减</text>
							<input type="digit" v-model="formData.reductionAmount" placeholder="请输入减免金额"/>
							<text class="unit">元</text>
						</view>
					</view>
				</view>
				
				<!-- 活动描述 -->
				<view class="form-item">
					<text class="form-label">活动说明</text>
					<view class="input-box">
						<textarea class="textarea" v-model="formData.description" placeholder="请输入活动说明"/>
					</view>
				</view>
				
				<view class="form-actions">
					<view class="cancel-btn" @click="cancelForm">取消</view>
					<view class="confirm-btn" @click="savePromotion">保存</view>
				</view>
			</view>
		</view>
		
		<!-- 新增按钮 -->
		<view class="add-promotion-btn" @click="createPromotion" v-if="!showForm">
			<uni-icons type="plusempty" size="20" color="#FFFFFF"></uni-icons>
			<text>创建优惠活动</text>
		</view>
		
		<!-- 日期选择器弹窗 -->
		<uni-calendar
			:insert="false"
			:start-date="startDate"
			:end-date="endDate"
			v-model="showDatePicker"
			@confirm="dateConfirm"
		/>
	</view>
</template>

<script>
	export default {
		data() {
			const today = new Date();
			const year = today.getFullYear();
			const month = today.getMonth();
			const day = today.getDate();
			
			// 默认开始日期为今天
			const startDate = this.formatDate(today);
			
			// 默认结束日期为一个月后
			const endDate = this.formatDate(new Date(year, month + 1, day));
			
			return {
				// 优惠活动列表
				promotionList: [],
				
				// 表单控制
				showForm: false,
				isEdit: false,
				editIndex: -1,
				
				// 类型选择
				typeOptions: ['折扣优惠', '满减优惠'],
				typeValues: ['discount', 'fullReduction'],
				typeIndex: 0,
				
				// 表单数据
				formData: {
					name: '',
					startDate: startDate,
					endDate: endDate,
					type: 'discount',
					discountValue: '',
					fullAmount: '',
					reductionAmount: '',
					description: '',
					status: 0 // 0: 未开始, 1: 进行中, 2: 已结束
				},
				
				// 日期选择器
				showDatePicker: false,
				dateType: 'start',
				startDate: '', 
				endDate: ''
			}
		},
		onLoad() {
			// 加载优惠活动数据
			this.loadPromotions();
		},
		methods: {
			formatDate(date) {
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			loadPromotions() {
				// 从本地存储加载优惠活动数据
				const promotions = uni.getStorageSync('promotions');
				if (promotions) {
					this.promotionList = JSON.parse(promotions);
					
					// 更新活动状态
					this.updatePromotionsStatus();
				}
			},
			updatePromotionsStatus() {
				const today = new Date();
				const todayStr = this.formatDate(today);
				
				this.promotionList.forEach(item => {
					if (todayStr < item.startDate) {
						item.status = 0; // 未开始
					} else if (todayStr > item.endDate) {
						item.status = 2; // 已结束
					} else if (item.status !== 3) { // 如果不是手动停止的
						item.status = 1; // 进行中
					}
				});
				
				// 保存更新后的数据
				this.savePromotionsToStorage();
			},
			getStatusClass(status) {
				switch (status) {
					case 0: return 'tag-waiting';
					case 1: return 'tag-active';
					case 2: return 'tag-ended';
					case 3: return 'tag-stopped';
					default: return '';
				}
			},
			getStatusText(status) {
				switch (status) {
					case 0: return '未开始';
					case 1: return '进行中';
					case 2: return '已结束';
					case 3: return '已停止';
					default: return '';
				}
			},
			getTypeText(type) {
				switch (type) {
					case 'discount': return '折扣优惠';
					case 'fullReduction': return '满减优惠';
					default: return '';
				}
			},
			getPromotionContent(item) {
				if (item.type === 'discount') {
					return `${item.discountValue}折优惠`;
				} else if (item.type === 'fullReduction') {
					return `满${item.fullAmount}元减${item.reductionAmount}元`;
				}
				return '';
			},
			createPromotion() {
				this.isEdit = false;
				this.editIndex = -1;
				this.formData = {
					name: '',
					startDate: this.formatDate(new Date()),
					endDate: this.formatDate(new Date(new Date().setMonth(new Date().getMonth() + 1))),
					type: 'discount',
					discountValue: '',
					fullAmount: '',
					reductionAmount: '',
					description: '',
					status: 0
				};
				this.typeIndex = 0;
				this.showForm = true;
			},
			editPromotion(index) {
				this.isEdit = true;
				this.editIndex = index;
				
				// 深拷贝数据避免直接修改
				const item = JSON.parse(JSON.stringify(this.promotionList[index]));
				this.formData = item;
				
				// 设置类型索引
				this.typeIndex = this.typeValues.indexOf(item.type);
				if (this.typeIndex === -1) {
					this.typeIndex = 0;
				}
				
				this.showForm = true;
			},
			cancelForm() {
				this.showForm = false;
			},
			openDatePicker(type) {
				this.dateType = type;
				this.showDatePicker = true;
			},
			dateConfirm(e) {
				const date = e.fulldate;
				if (this.dateType === 'start') {
					this.formData.startDate = date;
					
					// 如果开始日期晚于结束日期，自动更新结束日期
					if (date > this.formData.endDate) {
						this.formData.endDate = date;
					}
				} else {
					this.formData.endDate = date;
					
					// 如果结束日期早于开始日期，自动更新开始日期
					if (date < this.formData.startDate) {
						this.formData.startDate = date;
					}
				}
				
				this.showDatePicker = false;
			},
			typeChange(e) {
				this.typeIndex = e.detail.value;
				this.formData.type = this.typeValues[this.typeIndex];
			},
			validateForm() {
				if (!this.formData.name) {
					uni.showToast({
						title: '请输入活动名称',
						icon: 'none'
					});
					return false;
				}
				
				if (!this.formData.startDate || !this.formData.endDate) {
					uni.showToast({
						title: '请选择活动时间',
						icon: 'none'
					});
					return false;
				}
				
				if (this.formData.type === 'discount') {
					if (!this.formData.discountValue) {
						uni.showToast({
							title: '请输入折扣力度',
							icon: 'none'
						});
						return false;
					}
					
					const discount = parseFloat(this.formData.discountValue);
					if (isNaN(discount) || discount <= 0 || discount >= 10) {
						uni.showToast({
							title: '请输入正确的折扣(0-10之间)',
							icon: 'none'
						});
						return false;
					}
				} else if (this.formData.type === 'fullReduction') {
					if (!this.formData.fullAmount) {
						uni.showToast({
							title: '请输入满额条件',
							icon: 'none'
						});
						return false;
					}
					
					if (!this.formData.reductionAmount) {
						uni.showToast({
							title: '请输入减免金额',
							icon: 'none'
						});
						return false;
					}
					
					const fullAmount = parseFloat(this.formData.fullAmount);
					const reductionAmount = parseFloat(this.formData.reductionAmount);
					
					if (isNaN(fullAmount) || fullAmount <= 0) {
						uni.showToast({
							title: '请输入正确的满额金额',
							icon: 'none'
						});
						return false;
					}
					
					if (isNaN(reductionAmount) || reductionAmount <= 0) {
						uni.showToast({
							title: '请输入正确的减免金额',
							icon: 'none'
						});
						return false;
					}
					
					if (reductionAmount >= fullAmount) {
						uni.showToast({
							title: '减免金额不能大于或等于满额金额',
							icon: 'none'
						});
						return false;
					}
				}
				
				return true;
			},
			savePromotion() {
				if (!this.validateForm()) {
					return;
				}
				
				// 更新状态
				const today = new Date();
				const todayStr = this.formatDate(today);
				
				if (todayStr < this.formData.startDate) {
					this.formData.status = 0; // 未开始
				} else if (todayStr > this.formData.endDate) {
					this.formData.status = 2; // 已结束
				} else {
					this.formData.status = 1; // 进行中
				}
				
				if (this.isEdit && this.editIndex >= 0) {
					// 编辑模式
					this.promotionList[this.editIndex] = JSON.parse(JSON.stringify(this.formData));
				} else {
					// 新增模式
					this.promotionList.push(JSON.parse(JSON.stringify(this.formData)));
				}
				
				// 保存到本地存储
				this.savePromotionsToStorage();
				
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
				
				this.showForm = false;
			},
			savePromotionsToStorage() {
				uni.setStorageSync('promotions', JSON.stringify(this.promotionList));
			},
			deletePromotion(index) {
				uni.showModal({
					title: '提示',
					content: '确定要删除该优惠活动吗？',
					success: (res) => {
						if (res.confirm) {
							this.promotionList.splice(index, 1);
							this.savePromotionsToStorage();
							
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},
			togglePromotionStatus(index) {
				const item = this.promotionList[index];
				
				if (item.status === 1) {
					// 停止活动
					item.status = 3; // 已停止
				} else {
					// 启用活动
					const today = new Date();
					const todayStr = this.formatDate(today);
					
					if (todayStr < item.startDate) {
						item.status = 0; // 未开始
					} else if (todayStr > item.endDate) {
						item.status = 2; // 已结束
					} else {
						item.status = 1; // 进行中
					}
				}
				
				this.savePromotionsToStorage();
				
				uni.showToast({
					title: item.status === 3 ? '已停止' : '已启用',
					icon: 'success'
				});
			}
		}
	}
</script>

<style lang="scss">
	.promotion-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding: 20rpx;
		padding-bottom: 150rpx;
	}
	
	.tips-card {
		display: flex;
		align-items: center;
		background-color: #F0F7FF;
		padding: 20rpx;
		border-radius: 8rpx;
		margin-bottom: 20rpx;
	}
	
	.tips-text {
		font-size: 24rpx;
		color: #007AFF;
		margin-left: 10rpx;
	}
	
	.promotion-list {
		margin-bottom: 20rpx;
	}
	
	.empty-tip {
		padding: 80rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
	
	.empty-image {
		width: 200rpx;
		margin-bottom: 20rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
	
	.promotion-item {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 20rpx;
	}
	
	.item-header {
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #F5F5F5;
		display: flex;
		align-items: center;
	}
	
	.tag {
		font-size: 22rpx;
		padding: 4rpx 12rpx;
		border-radius: 4rpx;
		margin-right: 15rpx;
	}
	
	.tag-waiting {
		background-color: #F0F7FF;
		color: #007AFF;
	}
	
	.tag-active {
		background-color: #E8FFF0;
		color: #00CC66;
	}
	
	.tag-ended {
		background-color: #F5F5F5;
		color: #999999;
	}
	
	.tag-stopped {
		background-color: #FFF6F0;
		color: #FF9500;
	}
	
	.item-title {
		flex: 1;
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.edit-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.item-content {
		padding: 20rpx 30rpx;
	}
	
	.info-row {
		display: flex;
		margin-bottom: 10rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
	
	.info-label {
		font-size: 26rpx;
		color: #999999;
		width: 150rpx;
	}
	
	.info-value {
		flex: 1;
		font-size: 26rpx;
		color: #333333;
	}
	
	.item-footer {
		display: flex;
		border-top: 1rpx solid #F5F5F5;
	}
	
	.footer-btn {
		flex: 1;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 26rpx;
		
		uni-icons {
			margin-right: 10rpx;
		}
	}
	
	.delete-btn {
		color: #FF3B30;
		border-right: 1rpx solid #F5F5F5;
	}
	
	.start-btn {
		color: #00CC66;
	}
	
	.stop-btn {
		color: #FF9500;
	}
	
	.add-promotion-btn {
		position: fixed;
		bottom: 50rpx;
		left: 50%;
		transform: translateX(-50%);
		background-color: #007AFF;
		width: 300rpx;
		height: 80rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #FFFFFF;
		font-size: 28rpx;
		box-shadow: 0 4rpx 10rpx rgba(0, 122, 255, 0.3);
		
		text {
			margin-left: 10rpx;
		}
	}
	
	.form-panel {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		top: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 999;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
	}
	
	.panel-header {
		background-color: #FFFFFF;
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;
		border-bottom: 1rpx solid #F5F5F5;
	}
	
	.panel-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.close-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.panel-content {
		background-color: #FFFFFF;
		padding: 30rpx;
		max-height: 80vh;
		overflow-y: auto;
	}
	
	.form-item {
		margin-bottom: 30rpx;
	}
	
	.form-label {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 15rpx;
		display: block;
	}
	
	.input-box {
		background-color: #F5F5F5;
		border-radius: 8rpx;
		height: 80rpx;
	}
	
	.input-box input {
		height: 80rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333333;
	}
	
	.picker-view {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333333;
	}
	
	.placeholder {
		color: #999999;
	}
	
	.date-picker-group {
		display: flex;
		align-items: center;
	}
	
	.date-picker {
		flex: 1;
		background-color: #F5F5F5;
		border-radius: 8rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333333;
	}
	
	.separator {
		padding: 0 20rpx;
		color: #999999;
	}
	
	.discount-input, .price-input {
		display: flex;
		align-items: center;
		padding: 0 20rpx;
	}
	
	.prefix {
		font-size: 28rpx;
		color: #666666;
		margin-right: 10rpx;
	}
	
	.unit {
		font-size: 28rpx;
		color: #666666;
		margin-left: 10rpx;
	}
	
	.textarea {
		background-color: #F5F5F5;
		border-radius: 8rpx;
		width: 100%;
		height: 200rpx;
		padding: 20rpx;
		font-size: 28rpx;
		color: #333333;
	}
	
	.form-actions {
		display: flex;
		margin-top: 40rpx;
	}
	
	.cancel-btn, .confirm-btn {
		flex: 1;
		height: 80rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
	}
	
	.cancel-btn {
		background-color: #F5F5F5;
		color: #666666;
		margin-right: 20rpx;
	}
	
	.confirm-btn {
		background-color: #007AFF;
		color: #FFFFFF;
		margin-left: 20rpx;
	}
</style> 