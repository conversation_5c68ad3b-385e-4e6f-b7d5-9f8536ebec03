import request from '../../utils/request.js';

// 预订管理相关API

/**
 * 获取预订列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} params.status 订单状态
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 * @param {string} params.type 预订类型 (table/coach)
 */
export const getBookingList = (params = {}) => {
  return request.get('/merchant/booking/list', params);
};

/**
 * 获取预订详情
 * @param {number} bookingId 预订ID
 */
export const getBookingDetail = (bookingId) => {
  return request.get(`/merchant/booking/detail/${bookingId}`);
};

/**
 * 更新预订状态
 * @param {number} bookingId 预订ID
 * @param {string} status 状态 (confirmed/cancelled/completed/refunded)
 * @param {string} remark 备注
 */
export const updateBookingStatus = (bookingId, status, remark = '') => {
  return request.post(`/merchant/booking/status/${bookingId}`, { status, remark });
};

/**
 * 取消预订
 * @param {number} bookingId 预订ID
 * @param {string} reason 取消原因
 */
export const cancelBooking = (bookingId, reason) => {
  return request.post(`/merchant/booking/cancel/${bookingId}`, { reason });
};

/**
 * 退款处理
 * @param {number} bookingId 预订ID
 * @param {number} refund_amount 退款金额
 * @param {string} reason 退款原因
 */
export const refundBooking = (bookingId, refund_amount, reason) => {
  return request.post(`/merchant/booking/refund/${bookingId}`, { 
    refund_amount, 
    reason 
  });
};

/**
 * 获取预订统计
 * @param {Object} params 查询参数
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 * @param {string} params.type 统计类型 (daily/weekly/monthly)
 */
export const getBookingStats = (params = {}) => {
  return request.get('/merchant/booking/stats', params);
};

/**
 * 导出预订数据
 * @param {Object} params 查询参数
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 * @param {string} params.format 导出格式 (excel/csv)
 */
export const exportBookingData = (params = {}) => {
  return request.get('/merchant/booking/export', params);
};

/**
 * 批量处理预订
 * @param {Array} bookingIds 预订ID数组
 * @param {string} action 操作类型 (confirm/cancel/complete)
 */
export const batchProcessBookings = (bookingIds, action) => {
  return request.post('/merchant/booking/batch', { 
    booking_ids: bookingIds, 
    action 
  });
}; 