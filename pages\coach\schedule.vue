<template>
	<view class="schedule-container">
		<!-- 头部信息 -->
		<view class="header-card">
			<view class="coach-info">
				<image class="coach-avatar" :src="coachInfo.avatar || '/static/images/default-coach.png'" mode="aspectFill"></image>
				<view class="coach-detail">
					<text class="coach-name">{{coachInfo.name}}</text>
					<text class="coach-specialty">{{coachInfo.specialty}}</text>
				</view>
			</view>
		</view>
		
		<!-- 日期选择 -->
		<view class="date-selector">
			<view class="date-arrow" @click="prevWeek">
				<uni-icons type="left" size="18" color="#666666"></uni-icons>
			</view>
			<view class="date-range">
				{{startDateText}} 至 {{endDateText}}
			</view>
			<view class="date-arrow" @click="nextWeek">
				<uni-icons type="right" size="18" color="#666666"></uni-icons>
			</view>
		</view>
		
		<!-- 时间表格 -->
		<view class="schedule-table">
			<!-- 表头 -->
			<view class="table-header">
				<view class="time-column">时间</view>
				<view class="day-column" v-for="(day, index) in weekDays" :key="index">
					<view class="day-name">{{day.dayOfWeek}}</view>
					<view class="day-date">{{day.date}}</view>
				</view>
			</view>
			
			<!-- 表格内容 -->
			<view class="table-content">
				<view class="time-row" v-for="(time, timeIndex) in timeSlots" :key="timeIndex">
					<view class="time-column">{{time}}</view>
					<view 
						class="schedule-cell" 
						v-for="(day, dayIndex) in weekDays" 
						:key="dayIndex"
						:class="{
							'cell-available': isAvailable(day.fullDate, time),
							'cell-occupied': isOccupied(day.fullDate, time),
							'cell-reserved': isReserved(day.fullDate, time)
						}"
						@click="toggleTimeSlot(day.fullDate, time)"
					>
						<text v-if="isReserved(day.fullDate, time)" class="cell-text">已预约</text>
						<text v-else-if="isAvailable(day.fullDate, time)" class="cell-text">可预约</text>
						<text v-else-if="isOccupied(day.fullDate, time)" class="cell-text">不可用</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 图例 -->
		<view class="legend">
			<view class="legend-item">
				<view class="legend-color available"></view>
				<text class="legend-text">可预约</text>
			</view>
			<view class="legend-item">
				<view class="legend-color occupied"></view>
				<text class="legend-text">不可用</text>
			</view>
			<view class="legend-item">
				<view class="legend-color reserved"></view>
				<text class="legend-text">已预约</text>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-actions">
			<button class="action-btn cancel-btn" @click="goBack">返回</button>
			<button class="action-btn primary-btn" @click="saveSchedule">保存排班</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				coachId: 0,
				coachInfo: {
					id: 0,
					name: '',
					avatar: '',
					specialty: ''
				},
				currentWeekStart: null,
				weekDays: [],
				timeSlots: [
					'09:00-10:00', '10:00-11:00', '11:00-12:00', 
					'14:00-15:00', '15:00-16:00', '16:00-17:00',
					'19:00-20:00', '20:00-21:00', '21:00-22:00'
				],
				scheduleData: [], // 存储所有排班数据
				reservations: [] // 存储已预约数据
			}
		},
		computed: {
			startDateText() {
				if (!this.currentWeekStart) return '';
				const date = new Date(this.currentWeekStart);
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			},
			endDateText() {
				if (!this.currentWeekStart) return '';
				const date = new Date(this.currentWeekStart);
				date.setDate(date.getDate() + 6);
				return `${date.getMonth() + 1}月${date.getDate()}日`;
			}
		},
		onLoad(options) {
			if (options.id) {
				this.coachId = options.id;
				this.coachInfo.name = options.name || '';
			}
			
			// 初始化当前周的开始日期（周一）
			this.initCurrentWeek();
			
			// 初始化周数据
			this.generateWeekDays();
			
			// 加载教练数据
			this.loadCoachInfo();
			
			// 加载排班数据
			this.loadScheduleData();
			
			// 加载预约数据
			this.loadReservations();
		},
		methods: {
			initCurrentWeek() {
				const today = new Date();
				const day = today.getDay() || 7; // 获取当前是周几，周日为0，转换为7
				const diff = day - 1; // 计算与周一的差值
				
				// 设置为本周一
				const monday = new Date(today);
				monday.setDate(today.getDate() - diff);
				monday.setHours(0, 0, 0, 0);
				
				this.currentWeekStart = monday.toISOString().split('T')[0];
			},
			generateWeekDays() {
				const dayNames = ['一', '二', '三', '四', '五', '六', '日'];
				this.weekDays = [];
				
				for (let i = 0; i < 7; i++) {
					const date = new Date(this.currentWeekStart);
					date.setDate(date.getDate() + i);
					
					const fullDate = date.toISOString().split('T')[0];
					const month = date.getMonth() + 1;
					const day = date.getDate();
					
					this.weekDays.push({
						dayOfWeek: '周' + dayNames[i],
						date: `${month}/${day}`,
						fullDate: fullDate
					});
				}
			},
			loadCoachInfo() {
				// 从API获取教练信息
				// 这里使用模拟数据
				this.coachInfo = {
					id: this.coachId,
					name: this.coachInfo.name || '李教练',
					avatar: '',
					specialty: '中式台球'
				};
			},
			loadScheduleData() {
				// 从API获取排班数据
				// 这里使用模拟数据
				uni.showLoading({
					title: '加载中'
				});
				
				setTimeout(() => {
					// 模拟数据：可用时间段
					this.scheduleData = [
						{ date: this.weekDays[0].fullDate, time: '09:00-10:00', status: 'available' },
						{ date: this.weekDays[0].fullDate, time: '10:00-11:00', status: 'available' },
						{ date: this.weekDays[0].fullDate, time: '14:00-15:00', status: 'available' },
						{ date: this.weekDays[1].fullDate, time: '15:00-16:00', status: 'available' },
						{ date: this.weekDays[1].fullDate, time: '16:00-17:00', status: 'available' },
						{ date: this.weekDays[2].fullDate, time: '09:00-10:00', status: 'available' },
						{ date: this.weekDays[2].fullDate, time: '19:00-20:00', status: 'available' },
						{ date: this.weekDays[3].fullDate, time: '20:00-21:00', status: 'available' },
						{ date: this.weekDays[4].fullDate, time: '21:00-22:00', status: 'available' },
						{ date: this.weekDays[4].fullDate, time: '10:00-11:00', status: 'available' },
						{ date: this.weekDays[4].fullDate, time: '11:00-12:00', status: 'available' },
						{ date: this.weekDays[6].fullDate, time: '14:00-15:00', status: 'available' },
						{ date: this.weekDays[6].fullDate, time: '15:00-16:00', status: 'available' }
					];
					
					uni.hideLoading();
				}, 500);
			},
			loadReservations() {
				// 从API获取预约数据
				// 这里使用模拟数据
				setTimeout(() => {
					this.reservations = [
						{ date: this.weekDays[0].fullDate, time: '09:00-10:00' },
						{ date: this.weekDays[2].fullDate, time: '09:00-10:00' },
						{ date: this.weekDays[4].fullDate, time: '10:00-11:00' }
					];
				}, 600);
			},
			isAvailable(date, time) {
				// 检查是否为可预约状态
				return this.scheduleData.some(item => 
					item.date === date && 
					item.time === time && 
					item.status === 'available'
				);
			},
			isOccupied(date, time) {
				// 检查是否为不可用状态
				const isScheduled = this.scheduleData.some(item => 
					item.date === date && 
					item.time === time
				);
				
				// 如果不在排班表中，则为不可用状态
				return !isScheduled;
			},
			isReserved(date, time) {
				// 检查是否已被预约
				return this.reservations.some(item => 
					item.date === date && 
					item.time === time
				);
			},
			toggleTimeSlot(date, time) {
				// 如果已预约，则不可修改
				if (this.isReserved(date, time)) {
					uni.showToast({
						title: '该时段已被预约，无法修改',
						icon: 'none'
					});
					return;
				}
				
				// 查找是否已存在该时间段
				const index = this.scheduleData.findIndex(item => 
					item.date === date && item.time === time
				);
				
				if (index !== -1) {
					// 如果存在，则删除（变为不可用）
					this.scheduleData.splice(index, 1);
				} else {
					// 如果不存在，则添加（变为可预约）
					this.scheduleData.push({
						date: date,
						time: time,
						status: 'available'
					});
				}
			},
			prevWeek() {
				// 前一周
				const date = new Date(this.currentWeekStart);
				date.setDate(date.getDate() - 7);
				this.currentWeekStart = date.toISOString().split('T')[0];
				this.generateWeekDays();
				this.loadScheduleData();
				this.loadReservations();
			},
			nextWeek() {
				// 后一周
				const date = new Date(this.currentWeekStart);
				date.setDate(date.getDate() + 7);
				this.currentWeekStart = date.toISOString().split('T')[0];
				this.generateWeekDays();
				this.loadScheduleData();
				this.loadReservations();
			},
			saveSchedule() {
				// 保存排班数据
				uni.showLoading({
					title: '保存中'
				});
				
				// 模拟API请求延迟
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: '排班已保存',
						icon: 'success'
					});
				}, 1000);
			},
			goBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style lang="scss">
	.schedule-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 150rpx;
	}
	
	.header-card {
		background-color: #FFFFFF;
		padding: 30rpx;
		margin-bottom: 20rpx;
	}
	
	.coach-info {
		display: flex;
		align-items: center;
	}
	
	.coach-avatar {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50rpx;
		background-color: #F5F5F5;
		margin-right: 20rpx;
	}
	
	.coach-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.coach-specialty {
		font-size: 24rpx;
		color: #666666;
	}
	
	.date-selector {
		display: flex;
		justify-content: space-between;
		align-items: center;
		background-color: #FFFFFF;
		padding: 20rpx 30rpx;
		margin-bottom: 20rpx;
	}
	
	.date-arrow {
		width: 60rpx;
		height: 60rpx;
		background-color: #F5F5F5;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.date-range {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.schedule-table {
		background-color: #FFFFFF;
		margin: 0 20rpx 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.table-header {
		display: flex;
		border-bottom: 1rpx solid #EEEEEE;
	}
	
	.time-column {
		width: 160rpx;
		padding: 20rpx 10rpx;
		font-size: 24rpx;
		color: #666666;
		text-align: center;
		background-color: #F9F9F9;
		border-right: 1rpx solid #EEEEEE;
	}
	
	.day-column {
		flex: 1;
		padding: 10rpx;
		text-align: center;
		border-right: 1rpx solid #EEEEEE;
		
		&:last-child {
			border-right: none;
		}
	}
	
	.day-name {
		font-size: 24rpx;
		color: #333333;
		font-weight: bold;
	}
	
	.day-date {
		font-size: 22rpx;
		color: #666666;
	}
	
	.table-content {
		max-height: 800rpx;
		overflow-y: auto;
	}
	
	.time-row {
		display: flex;
		border-bottom: 1rpx solid #EEEEEE;
		
		&:last-child {
			border-bottom: none;
		}
	}
	
	.schedule-cell {
		flex: 1;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-right: 1rpx solid #EEEEEE;
		background-color: #F5F5F5;
		
		&:last-child {
			border-right: none;
		}
	}
	
	.cell-available {
		background-color: #EBFFEC;
	}
	
	.cell-occupied {
		background-color: #F5F5F5;
	}
	
	.cell-reserved {
		background-color: #FFF3E0;
	}
	
	.cell-text {
		font-size: 24rpx;
	}
	
	.legend {
		display: flex;
		justify-content: center;
		margin: 20rpx 0;
	}
	
	.legend-item {
		display: flex;
		align-items: center;
		margin: 0 20rpx;
	}
	
	.legend-color {
		width: 30rpx;
		height: 30rpx;
		border-radius: 6rpx;
		margin-right: 10rpx;
	}
	
	.legend-color.available {
		background-color: #EBFFEC;
		border: 1rpx solid #00CC66;
	}
	
	.legend-color.occupied {
		background-color: #F5F5F5;
		border: 1rpx solid #CCCCCC;
	}
	
	.legend-color.reserved {
		background-color: #FFF3E0;
		border: 1rpx solid #FF9500;
	}
	
	.legend-text {
		font-size: 24rpx;
		color: #666666;
	}
	
	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #FFFFFF;
		padding: 20rpx;
		display: flex;
		justify-content: space-between;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.action-btn {
		flex: 1;
		margin: 0 10rpx;
		border-radius: 40rpx;
		padding: 20rpx 0;
		font-size: 28rpx;
		text-align: center;
	}
	
	.cancel-btn {
		background-color: #F5F5F5;
		color: #666666;
	}
	
	.primary-btn {
		background-color: #007AFF;
		color: #FFFFFF;
	}
</style> 