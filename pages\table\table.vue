<template>
	<view class="table-container">
		<!-- 开发者测试面板 -->
		<view class="dev-panel" v-if="showDevPanel">
			<view class="dev-header">
				<text class="dev-title">🔧 开发者测试面板</text>
				<button class="dev-close" @click="showDevPanel = false">×</button>
			</view>
			<view class="dev-actions">
				<button class="dev-btn" @click="simulateLogin">模拟登录</button>
				<button class="dev-btn" @click="testGetMerchantInfo">获取商家信息</button>
				<button class="dev-btn" @click="testTableAPI">测试台桌API</button>
				<button class="dev-btn" @click="testCoachAPI">测试教练API</button>
				<button class="dev-btn" @click="testBookingAPI">测试预订API</button>
			</view>
			<view class="dev-log" v-if="devLogs.length > 0">
				<text class="log-title">操作日志:</text>
				<view class="log-item" v-for="(log, index) in devLogs" :key="index">
					<text class="log-time">{{ log.time }}</text>
					<text class="log-msg" :class="log.type">{{ log.message }}</text>
				</view>
			</view>
		</view>
		
		<!-- 开发者模式触发按钮（长按3秒激活） -->
		<view class="dev-trigger" @longpress="activateDevMode"></view>
		
		<!-- 状态过滤条 -->
		<view class="status-filter">
			<view class="filter-item" :class="{active: activeFilter === 'all'}" @click="filterTables('all')">
				全部 ({{getTotalCount()}})
			</view>
			<view class="filter-item" :class="{active: activeFilter === 'available'}" @click="filterTables('available')">
				空闲 ({{getCountByStatus('0')}})
			</view>
			<view class="filter-item" :class="{active: activeFilter === 'occupied'}" @click="filterTables('occupied')">
				占用 ({{getCountByStatus('1')}})
			</view>
			<view class="filter-item" :class="{active: activeFilter === 'maintenance'}" @click="filterTables('maintenance')">
				维修 ({{getCountByStatus('2')}})
			</view>
		</view>
		
		<!-- 操作栏 -->
		<view class="action-bar">
			<view class="search-box">
				<uni-icons type="search" size="18" color="#999"></uni-icons>
				<input class="search-input" type="text" v-model="searchKey" placeholder="搜索台球桌" @confirm="searchTables"/>
			</view>
			<button class="add-btn" @click="navigateTo('/pages/table/detail?type=add')">添加</button>
		</view>
		
		<!-- 台球桌网格视图 -->
		<view class="table-grid">
			<view class="table-item" v-for="(table, index) in filteredTables" :key="index" @click="navigateTo('/pages/table/detail?id='+table.id)">
				<view class="table-card" :class="'status-'+table.status">
					<view class="table-header">
						<text class="table-name">{{table.name}}</text>
						<view class="table-status">{{getStatusText(table.status)}}</view>
					</view>
					<view class="table-content">
						<view class="table-info">
							<view class="info-item">
								<text class="label">类型：</text>
								<text class="value">{{table.type}}</text>
							</view>
							<view class="info-item">
								<text class="label">位置：</text>
								<text class="value">{{table.location}}</text>
							</view>
							<view class="info-item">
								<text class="label">价格：</text>
								<text class="value price">¥{{table.price}}/小时</text>
							</view>
						</view>
						
						<view class="table-actions">
							<view class="action-item" @click.stop="changeTableStatus(table, '0')" v-if="table.status !== '0'">
								<uni-icons type="checkbox" size="16" color="#00CC66"></uni-icons>
								<text class="action-text">空闲</text>
							</view>
							<view class="action-item" @click.stop="changeTableStatus(table, '1')" v-if="table.status !== '1'">
								<uni-icons type="locked" size="16" color="#FF9500"></uni-icons>
								<text class="action-text">占用</text>
							</view>
							<view class="action-item" @click.stop="changeTableStatus(table, '2')" v-if="table.status !== '2'">
								<uni-icons type="gear" size="16" color="#FF3B30"></uni-icons>
								<text class="action-text">维修</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-tip" v-if="filteredTables.length === 0">
				<image class="empty-icon" src="/static/images/empty.png"></image>
				<text class="empty-text">暂无台球桌数据</text>
			</view>
		</view>
		
		<!-- 悬浮添加按钮 -->
		<view class="float-add-btn" @click="navigateTo('/pages/table/detail?type=add')">
			<uni-icons type="plusempty" size="24" color="#FFFFFF"></uni-icons>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { getTableList, setTableStatus, addTable } from '../../api/merchant/table.js';
import { getMerchantInfo, merchantLogin } from '../../api/merchant/auth.js';
import { getCoachList, addCoach } from '../../api/merchant/coach.js';
import { getBookingList } from '../../api/merchant/booking.js';
import { getDashboardOverview } from '../../api/merchant/stats.js';

// 响应式数据
const activeFilter = ref('all');
const searchKey = ref('');
const showDevPanel = ref(false);
const devLogs = ref([]);

const tables = ref([
	{
		id: 1,
		name: '1号台',
		type: '中式黑八',
		size: '9尺',
		location: '一楼中区',
		price: 30,
		status: '0', // 0: 空闲，1: 占用，2: 维修中
		lastMaintenance: '2023-08-15',
		remark: '新换台呢'
	},
	{
		id: 2,
		name: '2号台',
		type: '美式九球',
		size: '9尺',
		location: '一楼中区',
		price: 35,
		status: '1',
		lastMaintenance: '2023-08-10',
		remark: ''
	},
	{
		id: 3,
		name: '3号台',
		type: '斯诺克',
		size: '12尺',
		location: '二楼VIP区',
		price: 50,
		status: '0',
		lastMaintenance: '2023-07-20',
		remark: ''
	},
	{
		id: 4,
		name: '4号台',
		type: '中式黑八',
		size: '9尺',
		location: '一楼角落',
		price: 30,
		status: '2',
		lastMaintenance: '2023-09-01',
		remark: '台呢撕裂，需要更换'
	}
]);

// 计算属性
const filteredTables = computed(() => {
	let result = [...tables.value];
	
	// 按搜索关键词过滤
	if (searchKey.value) {
		result = result.filter(table => 
			table.name.includes(searchKey.value) || 
			table.type.includes(searchKey.value) ||
			table.location.includes(searchKey.value)
		);
	}
	
	// 按状态过滤
	if (activeFilter.value === 'available') {
		result = result.filter(table => table.status === '0');
	} else if (activeFilter.value === 'occupied') {
		result = result.filter(table => table.status === '1');
	} else if (activeFilter.value === 'maintenance') {
		result = result.filter(table => table.status === '2');
	}
	
	return result;
});

// 页面加载时
onMounted(() => {
	loadTables();
});

// 开发者功能
const addDevLog = (message, type = 'info') => {
	const now = new Date();
	const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
	
	devLogs.value.unshift({
		time,
		message,
		type
	});
	
	// 限制日志数量
	if (devLogs.value.length > 20) {
		devLogs.value = devLogs.value.slice(0, 20);
	}
};

const activateDevMode = () => {
	showDevPanel.value = true;
	addDevLog('开发者模式已激活', 'success');
	
	uni.showToast({
		title: '开发者模式已激活',
		icon: 'success'
	});
};

const simulateLogin = () => {
	addDevLog('开始模拟登录...', 'info');
	
	// 模拟登录
	const mockToken = 'mock-token-' + Date.now();
	const mockMerchantInfo = {
		id: 123,
		name: '星辰台球厅',
		phone: '13800138000',
		avatar: '/logo.png',
		status: '1'
	};
	
	uni.setStorageSync('merchant_token', mockToken);
	uni.setStorageSync('merchant_info', JSON.stringify(mockMerchantInfo));
	
	addDevLog('模拟登录成功', 'success');
	uni.showToast({
		title: '模拟登录成功',
		icon: 'success'
	});
};

const testGetMerchantInfo = async () => {
	addDevLog('测试获取商家信息...', 'info');
	
	try {
		const response = await getMerchantInfo();
		addDevLog('获取商家信息成功: ' + JSON.stringify(response.data), 'success');
	} catch (error) {
		addDevLog('API失败，使用模拟数据', 'warning');
		// 模拟返回数据
		const mockData = {
			id: 123,
			name: '星辰台球厅',
			contact_phone: '13800138000',
			address: '北京市朝阳区建国路88号'
		};
		addDevLog('模拟数据: ' + JSON.stringify(mockData), 'info');
	}
};

const testTableAPI = async () => {
	addDevLog('测试台桌管理API...', 'info');
	
	try {
		// 测试获取台桌列表
		const listResponse = await getTableList();
		addDevLog('获取台桌列表成功', 'success');
		
		// 测试添加台桌
		const addResponse = await addTable({
			name: '测试台桌',
			type: '九球台',
			hourly_rate: 50,
			description: 'API测试台桌'
		});
		addDevLog('添加台桌成功', 'success');
		
	} catch (error) {
		addDevLog('台桌API测试失败，使用模拟数据', 'warning');
		// 添加一个新的模拟台桌
		const newTable = {
			id: Date.now(),
			name: '测试台桌',
			type: '九球台',
			location: '测试区',
			price: 50,
			status: '0',
			lastMaintenance: new Date().toISOString().split('T')[0],
			remark: 'API测试添加'
		};
		tables.value.push(newTable);
		addDevLog('已添加模拟台桌', 'success');
	}
};

const testCoachAPI = async () => {
	addDevLog('测试教练管理API...', 'info');
	
	try {
		const response = await getCoachList();
		addDevLog('获取教练列表成功', 'success');
	} catch (error) {
		addDevLog('教练API测试失败，使用模拟数据', 'warning');
		const mockData = {
			list: [
				{ id: 1, name: '张教练', skill_level: '高级', hourly_rate: 100 },
				{ id: 2, name: '李教练', skill_level: '中级', hourly_rate: 80 }
			]
		};
		addDevLog('模拟教练数据: ' + JSON.stringify(mockData), 'info');
	}
};

const testBookingAPI = async () => {
	addDevLog('测试预订管理API...', 'info');
	
	try {
		const response = await getBookingList();
		addDevLog('获取预订列表成功', 'success');
	} catch (error) {
		addDevLog('预订API测试失败，使用模拟数据', 'warning');
		const mockData = {
			list: [
				{ id: 1, type: 'table', status: 'pending', user_name: '用户A' },
				{ id: 2, type: 'coach', status: 'confirmed', user_name: '用户B' }
			]
		};
		addDevLog('模拟预订数据: ' + JSON.stringify(mockData), 'info');
	}
};

// 加载台球桌数据
const loadTables = async () => {
	try {
		uni.showLoading({
			title: '加载中...'
		});

		const response = await getTableList({
			page: 1,
			pageSize: 100,
			search: searchKey.value,
			status: activeFilter.value === 'all' ? '' : getStatusValueByFilter(activeFilter.value)
		});

		uni.hideLoading();

		if (response && response.data) {
			tables.value = response.data.list.map(table => ({
				id: table.id,
				name: table.name,
				type: table.type,
				size: table.size,
				location: table.location,
				price: table.price_per_hour,
				status: table.status,
				lastMaintenance: table.last_maintenance_date,
				remark: table.remark || ''
			}));
		}
	} catch (error) {
		uni.hideLoading();
		console.warn('API获取失败，使用模拟数据');
		// 保持现有的模拟数据
	}
};

// 根据过滤器获取状态值
const getStatusValueByFilter = (filter) => {
	const filterMap = {
		'available': '0',
		'occupied': '1', 
		'maintenance': '2'
	};
	return filterMap[filter] || '';
};

// 过滤台球桌
const filterTables = (type) => {
	activeFilter.value = type;
	// 重新加载数据
	loadTables();
};

// 搜索台球桌
const searchTables = () => {
	// 重新加载数据
	loadTables();
};

// 导航到指定页面
const navigateTo = (url) => {
	uni.navigateTo({
		url: url
	});
};

// 修改台球桌状态
const changeTableStatus = async (table, status) => {
	const statusText = getStatusText(status);
	uni.showModal({
		title: '确认操作',
		content: `确定将【${table.name}】状态修改为"${statusText}"吗？`,
		success: async (res) => {
			if (res.confirm) {
				try {
					uni.showLoading({
						title: '更新中...'
					});

					const response = await setTableStatus({
						table_id: table.id,
						status: status
					});

					uni.hideLoading();

					if (response.code === 200) {
						// 更新本地数据
						const index = tables.value.findIndex(t => t.id === table.id);
						if (index !== -1) {
							tables.value[index].status = status;
						}
						
						uni.showToast({
							title: '状态已更新',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: response.msg || '状态更新失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('更新台球桌状态失败', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				}
			}
		}
	});
};

// 获取状态文本
const getStatusText = (status) => {
	const statusMap = {
		'0': '空闲',
		'1': '占用',
		'2': '维修中'
	};
	return statusMap[status] || '未知';
};

// 获取总数量
const getTotalCount = () => {
	return tables.value.length;
};

// 按状态获取数量
const getCountByStatus = (status) => {
	return tables.value.filter(table => table.status === status).length;
};
</script>

<style lang="scss">
	.table-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 30rpx;
	}
	
	.status-filter {
		display: flex;
		background-color: #FFFFFF;
		padding: 0 20rpx;
		overflow-x: auto;
		white-space: nowrap;
	}
	
	.filter-item {
		padding: 20rpx 30rpx;
		font-size: 28rpx;
		color: #666666;
		position: relative;
		flex-shrink: 0;
	}
	
	.filter-item.active {
		color: #007AFF;
		font-weight: bold;
		
		&::after {
			content: '';
			position: absolute;
			left: 30rpx;
			right: 30rpx;
			bottom: 0;
			height: 4rpx;
			background-color: #007AFF;
			border-radius: 2rpx;
		}
	}
	
	.action-bar {
		padding: 20rpx;
		background-color: #FFFFFF;
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		border-bottom: 1rpx solid #EEEEEE;
	}
	
	.search-box {
		flex: 1;
		height: 72rpx;
		background-color: #F5F5F5;
		border-radius: 36rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		margin-right: 20rpx;
	}
	
	.search-input {
		flex: 1;
		height: 72rpx;
		font-size: 28rpx;
		margin-left: 10rpx;
	}
	
	.add-btn {
		width: 160rpx;
		height: 72rpx;
		background-color: #007AFF;
		color: #FFFFFF;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 36rpx;
		padding: 0;
	}
	
	.table-grid {
		padding: 0 20rpx;
		display: flex;
		flex-wrap: wrap;
	}
	
	.table-item {
		width: 50%;
		padding: 10rpx;
		box-sizing: border-box;
	}
	
	.table-card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		height: 100%;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		border-top: 6rpx solid #CCCCCC;
	}
	
	.status-0 {
		border-top-color: #00CC66;
	}
	
	.status-1 {
		border-top-color: #FF9500;
	}
	
	.status-2 {
		border-top-color: #FF3B30;
	}
	
	.table-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
		border-bottom: 1rpx solid #F5F5F5;
		padding-bottom: 16rpx;
	}
	
	.table-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.table-status {
		font-size: 24rpx;
		padding: 4rpx 12rpx;
		border-radius: 6rpx;
	}
	
	.status-0 .table-status {
		background-color: rgba(0, 204, 102, 0.1);
		color: #00CC66;
	}
	
	.status-1 .table-status {
		background-color: rgba(255, 149, 0, 0.1);
		color: #FF9500;
	}
	
	.status-2 .table-status {
		background-color: rgba(255, 59, 48, 0.1);
		color: #FF3B30;
	}
	
	.table-content {
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	.table-info {
		flex: 1;
	}
	
	.info-item {
		display: flex;
		margin-bottom: 10rpx;
		font-size: 26rpx;
	}
	
	.label {
		color: #999999;
		width: 100rpx;
	}
	
	.value {
		color: #333333;
		flex: 1;
	}
	
	.price {
		color: #FF3B30;
		font-weight: bold;
	}
	
	.table-actions {
		display: flex;
		justify-content: space-around;
		margin-top: 16rpx;
		border-top: 1rpx solid #F5F5F5;
		padding-top: 16rpx;
	}
	
	.action-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.action-text {
		font-size: 24rpx;
		color: #666666;
		margin-top: 6rpx;
	}
	
	.float-add-btn {
		position: fixed;
		right: 30rpx;
		bottom: 100rpx;
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		background-color: #007AFF;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
	}
	
	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 100rpx 0;
		width: 100%;
	}
	
	.empty-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
</style> 