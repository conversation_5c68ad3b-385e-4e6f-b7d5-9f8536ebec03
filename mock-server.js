const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 8000;

// 启用CORS和JSON解析
app.use(cors({
  origin: ['http://localhost:5173', 'http://127.0.0.1:5173'],
  credentials: true
}));
app.use(express.json());

// 模拟数据
const mockMerchant = {
  id: 1,
  name: '测试台球厅',
  contact_person: '张三',
  contact_phone: '13800138000',
  address: '北京市朝阳区测试路123号',
  status: 'active'
};

const mockToken = 'mock_token_12345';

// 商家认证接口
app.post('/api/merchant/auth/login', (req, res) => {
  const { phone, password } = req.body;
  
  console.log('登录请求:', { phone, password });
  
  if (phone === '13800138000' && password === '123456') {
    res.json({
      code: '0000',
      message: '登录成功',
      data: {
        token: mockToken,
        merchant_info: mockMerchant
      }
    });
  } else {
    res.json({
      code: '1002',
      message: '手机号或密码错误'
    });
  }
});

app.post('/api/merchant/auth/register', (req, res) => {
  console.log('注册请求:', req.body);
  
  res.json({
    code: '0000',
    message: '注册成功',
    data: {
      merchant_id: 2,
      message: '商家注册成功，请等待审核'
    }
  });
});

app.get('/api/merchant/auth/info', (req, res) => {
  const token = req.headers.authorization;
  
  if (token && token.includes(mockToken)) {
    res.json({
      code: '0000',
      message: '获取成功',
      data: mockMerchant
    });
  } else {
    res.json({
      code: '1001',
      message: 'token无效或已过期'
    });
  }
});

// 台桌管理接口
app.get('/api/merchant/table/list', (req, res) => {
  res.json({
    code: '0000',
    message: '获取成功',
    data: {
      list: [
        { id: 1, name: '1号桌', type: 'standard', status: 'available', hourly_rate: 50 },
        { id: 2, name: '2号桌', type: 'vip', status: 'occupied', hourly_rate: 80 }
      ],
      total: 2
    }
  });
});

app.post('/api/merchant/table/add', (req, res) => {
  console.log('添加台桌:', req.body);
  
  res.json({
    code: '0000',
    message: '添加成功',
    data: {
      table_id: 3
    }
  });
});

// 教练管理接口
app.get('/api/merchant/coach/list', (req, res) => {
  res.json({
    code: '0000',
    message: '获取成功',
    data: {
      list: [
        { id: 1, name: '张教练', skill_level: 'expert', status: 'active', hourly_rate: 100 },
        { id: 2, name: '李教练', skill_level: 'intermediate', status: 'active', hourly_rate: 80 }
      ],
      total: 2
    }
  });
});

// 预订管理接口
app.get('/api/merchant/booking/list', (req, res) => {
  res.json({
    code: '0000',
    message: '获取成功',
    data: {
      list: [
        { id: 1, type: 'table', table_name: '1号桌', customer_name: '王五', status: 'confirmed', amount: 100 },
        { id: 2, type: 'coach', coach_name: '张教练', customer_name: '赵六', status: 'completed', amount: 200 }
      ],
      total: 2
    }
  });
});

// 统计接口
app.get('/api/merchant/stats/dashboard', (req, res) => {
  res.json({
    code: '0000',
    message: '获取成功',
    data: {
      today_revenue: 1200,
      today_bookings: 8,
      table_utilization: 75,
      active_coaches: 3
    }
  });
});

// 处理所有其他接口
app.all('/api/*', (req, res) => {
  console.log(`API请求: ${req.method} ${req.path}`);
  
  res.json({
    code: '0000',
    message: '模拟接口调用成功',
    data: {
      method: req.method,
      path: req.path,
      body: req.body,
      query: req.query
    }
  });
});

// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
  console.log(`模拟API服务器运行在 http://localhost:${PORT}`);
  console.log('支持的测试账号: 13800138000 / 123456');
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    code: '9999',
    message: '服务器内部错误'
  });
}); 