<template>
	<div class="api-test-container">
		<div class="test-header">
			<h1>🎱 商家端API功能测试</h1>
			<div class="login-status" :class="{ 'logged-in': isLoggedIn }">
				<span>{{ isLoggedIn ? '已登录' : '未登录' }}</span>
				<span v-if="merchantInfo.name">({{ merchantInfo.name }})</span>
			</div>
		</div>

		<!-- 认证功能测试 -->
		<div class="test-section">
			<h2>🔐 商家认证功能</h2>
			
			<div class="test-group">
				<h3>模拟登录</h3>
				<div class="form-row">
					<input v-model="loginForm.phone" placeholder="手机号" class="test-input" />
					<input v-model="loginForm.password" type="password" placeholder="密码" class="test-input" />
					<button @click="testLogin" class="test-btn primary">登录测试</button>
				</div>
			</div>
			
			<div class="test-group">
				<h3>获取商家信息</h3>
				<button @click="testGetMerchantInfo" class="test-btn">获取信息</button>
				<pre v-if="testResults.merchantInfo" class="result-display">{{ JSON.stringify(testResults.merchantInfo, null, 2) }}</pre>
			</div>
		</div>

		<!-- 台桌管理功能测试 -->
		<div class="test-section">
			<h2>🎱 台桌管理功能</h2>
			
			<div class="test-group">
				<h3>获取台桌列表</h3>
				<button @click="testGetTableList" class="test-btn">获取列表</button>
				<pre v-if="testResults.tableList" class="result-display">{{ JSON.stringify(testResults.tableList, null, 2) }}</pre>
			</div>
		</div>

		<!-- 操作日志 -->
		<div class="test-section">
			<h2>📝 操作日志</h2>
			<div class="log-container">
				<div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
					<span class="log-time">{{ log.time }}</span>
					<span class="log-message">{{ log.message }}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { merchantLogin, getMerchantInfo } from '../../api/merchant/auth.js';
import { getTableList } from '../../api/merchant/table.js';

// 响应式数据
const isLoggedIn = ref(false);
const merchantInfo = ref({});
const logs = ref([]);
const testResults = ref({});

const loginForm = ref({
	phone: '13800138000',
	password: '123456'
});

// 页面加载时
onMounted(() => {
	addLog('页面加载完成', 'info');
	checkLoginStatus();
});

// 检查登录状态
const checkLoginStatus = () => {
	const token = uni.getStorageSync('merchant_token');
	if (token) {
		isLoggedIn.value = true;
		addLog('检测到已登录状态', 'success');
	}
};

// 添加日志
const addLog = (message, type = 'info') => {
	const now = new Date();
	const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
	
	logs.value.unshift({
		time,
		message,
		type
	});
};

// 测试登录
const testLogin = async () => {
	addLog(`开始测试登录: ${loginForm.value.phone}`, 'info');
	
	try {
		// 模拟登录
		const mockToken = 'mock-token-' + Date.now();
		const mockMerchantInfo = {
			id: 123,
			name: '星辰台球厅',
			phone: loginForm.value.phone
		};
		
		uni.setStorageSync('merchant_token', mockToken);
		uni.setStorageSync('merchant_info', JSON.stringify(mockMerchantInfo));
		
		isLoggedIn.value = true;
		merchantInfo.value = mockMerchantInfo;
		
		addLog('登录成功', 'success');
		
	} catch (error) {
		addLog(`登录失败: ${error.message}`, 'error');
	}
};

// 测试获取商家信息
const testGetMerchantInfo = async () => {
	addLog('开始测试获取商家信息', 'info');
	
	try {
		const mockData = {
			id: 123,
			name: '星辰台球厅',
			contact_phone: '13800138000',
			address: '北京市朝阳区建国路88号'
		};
		testResults.value.merchantInfo = mockData;
		addLog('获取商家信息成功', 'success');
	} catch (error) {
		addLog(`获取商家信息失败: ${error.message}`, 'error');
	}
};

// 测试获取台桌列表
const testGetTableList = async () => {
	addLog('开始测试获取台桌列表', 'info');
	
	try {
		const mockData = {
			list: [
				{ id: 1, name: '1号台桌', type: '九球台', status: 'available' },
				{ id: 2, name: '2号台桌', type: '斯诺克台', status: 'occupied' }
			],
			total: 2
		};
		testResults.value.tableList = mockData;
		addLog('获取台桌列表成功', 'success');
	} catch (error) {
		addLog(`获取台桌列表失败: ${error.message}`, 'error');
	}
};
</script>

<style scoped>
.api-test-container {
	padding: 20px;
	font-family: -apple-system, BlinkMacSystemFont, sans-serif;
}

.test-header {
	background: linear-gradient(135deg, #007AFF, #0051D5);
	color: white;
	padding: 30px;
	border-radius: 16px;
	margin-bottom: 30px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.test-section {
	background: white;
	border-radius: 12px;
	padding: 30px;
	margin-bottom: 30px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.test-group {
	margin-bottom: 20px;
	padding: 20px;
	border: 1px solid #E5E5E5;
	border-radius: 8px;
}

.form-row {
	display: flex;
	gap: 15px;
	margin-bottom: 15px;
}

.test-input {
	flex: 1;
	padding: 12px 16px;
	border: 2px solid #E5E5E5;
	border-radius: 8px;
}

.test-btn {
	padding: 12px 24px;
	border: none;
	border-radius: 8px;
	cursor: pointer;
	background: #F5F5F5;
	color: #333;
}

.test-btn.primary {
	background: #007AFF;
	color: white;
}

.result-display {
	background: #F8F9FA;
	border: 1px solid #E9ECEF;
	border-radius: 8px;
	padding: 20px;
	margin-top: 15px;
	font-size: 12px;
	overflow-x: auto;
}

.log-container {
	background: #1E1E1E;
	border-radius: 8px;
	padding: 20px;
	max-height: 400px;
	overflow-y: auto;
}

.log-item {
	display: flex;
	margin-bottom: 8px;
	font-family: monospace;
	font-size: 13px;
}

.log-time {
	color: #888;
	margin-right: 15px;
}

.log-message {
	color: #FFF;
}

.log-item.success .log-message {
	color: #00CC66;
}

.log-item.error .log-message {
	color: #FF4757;
}

.login-status {
	background: rgba(255, 255, 255, 0.2);
	padding: 10px 20px;
	border-radius: 25px;
}

.login-status.logged-in {
	background: rgba(0, 204, 102, 0.3);
}
</style> 