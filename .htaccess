# CORS配置
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS, PATCH"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, Accept, Origin, Cache-Control, Pragma"
Header always set Access-Control-Allow-Credentials "true"
Header always set Access-Control-Max-Age "86400"

# 处理预检请求
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# 确保PHP文件可以执行
<Files "*.php">
    Order allow,deny
    Allow from all
</Files> 