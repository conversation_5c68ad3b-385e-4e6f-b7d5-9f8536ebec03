import request from '../../utils/request.js';

// 文件上传相关API

/**
 * 上传单张图片
 * @param {File} file 图片文件
 * @param {string} type 文件类型：avatar/logo/table
 */
export const uploadImage = (file, type = 'avatar') => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: '/api/common/upload/image',
      filePath: file,
      name: 'file',
      formData: {
        type: type
      },
      header: {
        'Authorization': `Bearer ${uni.getStorageSync('merchant_token')}`
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          if (data.code === '0000') {
            resolve(data);
          } else {
            uni.showToast({
              title: data.message || '上传失败',
              icon: 'none'
            });
            reject(new Error(data.message || '上传失败'));
          }
        } catch (error) {
          uni.showToast({
            title: '上传失败',
            icon: 'none'
          });
          reject(error);
        }
      },
      fail: (error) => {
        uni.showToast({
          title: '上传失败',
          icon: 'none'
        });
        reject(error);
      }
    });
  });
};

/**
 * 批量上传图片
 * @param {Array} files 图片文件数组（最多9张）
 */
export const uploadImages = (files) => {
  return new Promise((resolve, reject) => {
    if (!files || files.length === 0) {
      reject(new Error('请选择要上传的图片'));
      return;
    }
    
    if (files.length > 9) {
      uni.showToast({
        title: '最多只能上传9张图片',
        icon: 'none'
      });
      reject(new Error('最多只能上传9张图片'));
      return;
    }

    // 使用uni.uploadFile的方式上传多个文件
    const uploadPromises = files.map(file => {
      return new Promise((fileResolve, fileReject) => {
        uni.uploadFile({
          url: '/api/common/upload/images',
          filePath: file,
          name: 'files[]',
          header: {
            'Authorization': `Bearer ${uni.getStorageSync('merchant_token')}`
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              if (data.code === '0000') {
                fileResolve(data.data);
              } else {
                fileReject(new Error(data.message || '上传失败'));
              }
            } catch (error) {
              fileReject(error);
            }
          },
          fail: fileReject
        });
      });
    });

    Promise.all(uploadPromises)
      .then(results => {
        resolve({
          code: '0000',
          message: '上传成功',
          data: {
            files: results.flat(),
            count: results.length
          }
        });
      })
      .catch(error => {
        uni.showToast({
          title: '部分图片上传失败',
          icon: 'none'
        });
        reject(error);
      });
  });
};

/**
 * 选择并上传图片
 * @param {Object} options 选择选项
 * @param {number} options.count 最多选择数量，默认1
 * @param {string} options.type 文件类型
 */
export const chooseAndUploadImage = (options = {}) => {
  const { count = 1, type = 'avatar' } = options;
  
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count: count,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        try {
          if (count === 1) {
            // 单张图片上传
            const result = await uploadImage(res.tempFilePaths[0], type);
            resolve(result);
          } else {
            // 多张图片上传
            const result = await uploadImages(res.tempFilePaths);
            resolve(result);
          }
        } catch (error) {
          reject(error);
        }
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
};
