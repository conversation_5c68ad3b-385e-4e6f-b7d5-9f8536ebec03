<template>
	<view class="detail-container">
		<form @submit="submitForm">
			<!-- 头部信息 -->
			<view class="header-card">
				<view class="avatar-box">
					<image class="avatar" :src="coachInfo.avatar || '/static/images/default-coach.png'" mode="aspectFill"></image>
					<view class="upload-btn" @click="chooseImage">
						<uni-icons type="camera" size="20" color="#FFFFFF"></uni-icons>
					</view>
				</view>
				<view class="base-info">
					<input class="coach-name" type="text" v-model="coachInfo.name" placeholder="请输入教练姓名" :disabled="viewMode"/>
					<view class="status-tag" :class="coachInfo.status === '1' ? 'status-working' : 'status-rest'">
						{{coachInfo.status === '1' ? '工作中' : '休息中'}}
					</view>
				</view>
			</view>
			
			<!-- 基本信息 -->
			<view class="info-card">
				<view class="card-title">基本信息</view>
				<view class="form-item">
					<text class="form-label">性别</text>
					<view class="form-content">
						<radio-group @change="genderChange" class="radio-group" :disabled="viewMode">
							<label class="radio">
								<radio value="男" :checked="coachInfo.gender === '男'" color="#007AFF"/>
								<text>男</text>
							</label>
							<label class="radio">
								<radio value="女" :checked="coachInfo.gender === '女'" color="#007AFF"/>
								<text>女</text>
							</label>
						</radio-group>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">年龄</text>
					<view class="form-content">
						<input type="number" v-model="coachInfo.age" placeholder="请输入年龄" :disabled="viewMode"/>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">电话</text>
					<view class="form-content">
						<input type="text" v-model="coachInfo.phone" placeholder="请输入电话号码" :disabled="viewMode"/>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">专长</text>
					<view class="form-content">
						<input type="text" v-model="coachInfo.specialty" placeholder="请输入专长，如：中式台球" :disabled="viewMode"/>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">教龄</text>
					<view class="form-content">
						<input type="number" v-model="coachInfo.experience" placeholder="请输入教龄(年)" :disabled="viewMode"/>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">小时收费</text>
					<view class="form-content">
						<input type="digit" v-model="coachInfo.pricePerHour" placeholder="请输入每小时收费(元)" :disabled="viewMode"/>
					</view>
				</view>
				<view class="form-item">
					<text class="form-label">状态</text>
					<view class="form-content">
						<picker @change="statusChange" :value="statusIndex" :range="statusOptions" :disabled="viewMode">
							<view class="picker-view">
								{{statusOptions[statusIndex]}}
								<uni-icons type="bottom" size="14" color="#666666"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
			</view>
			
			<!-- 技能标签 -->
			<view class="info-card">
				<view class="card-title">技能标签</view>
				<view class="tags-container">
					<view 
						class="tag-item" 
						v-for="(tag, index) in coachInfo.tags" 
						:key="index"
						:class="{active: selectedTags.includes(tag)}"
						@click="toggleTag(tag)"
						v-if="!viewMode"
					>
						{{tag}}
					</view>
					<view class="tag-item add-tag" @click="showAddTagDialog" v-if="!viewMode">
						<uni-icons type="plusempty" size="14" color="#007AFF"></uni-icons>
						<text>添加</text>
					</view>
					
					<view class="tag-item" v-for="(tag, index) in coachInfo.tags" :key="index" v-if="viewMode">
						{{tag}}
					</view>
					<view class="empty-tip" v-if="coachInfo.tags.length === 0 && viewMode">
						暂无技能标签
					</view>
				</view>
			</view>
			
			<!-- 教练简介 -->
			<view class="info-card">
				<view class="card-title">教练简介</view>
				<view class="form-item">
					<textarea 
						class="form-textarea" 
						v-model="coachInfo.introduction" 
						placeholder="请输入教练简介，如教学风格、获奖经历等" 
						:disabled="viewMode"
					/>
				</view>
			</view>
			
			<!-- 按钮区域 -->
			<view class="bottom-actions">
				<button class="action-btn cancel-btn" @click="goBack">返回</button>
				<button class="action-btn primary-btn" type="primary" form-type="submit" v-if="!viewMode">保存</button>
				<button class="action-btn edit-btn" @click="switchToEdit" v-if="viewMode && canEdit">编辑</button>
				<button class="action-btn schedule-btn" @click="goToSchedule" v-if="viewMode">排班</button>
			</view>
		</form>
		
		<!-- 添加标签弹窗 -->
		<uni-popup ref="tagPopup" type="dialog">
			<uni-popup-dialog
				title="添加技能标签"
				:type="dialogType"
				:before-close="true"
				confirmText="确定"
				cancelText="取消"
				@confirm="addNewTag"
				@close="closeDialog"
			>
				<uni-easyinput
					v-model="newTag"
					placeholder="请输入标签名称（如：斯诺克、九球等）"
					:maxlength="10"
				/>
			</uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: 0,
				type: '', // 'add': 新增, 'edit': 编辑, 'view': 查看
				viewMode: true,
				canEdit: true,
				coachInfo: {
					id: 0,
					name: '',
					gender: '男',
					age: '',
					phone: '',
					avatar: '',
					specialty: '',
					experience: '',
					status: '1', // 1: 工作中, 0: 休息中
					pricePerHour: '',
					tags: [],
					introduction: ''
				},
				statusIndex: 0,
				statusOptions: ['休息中', '工作中'],
				statusValues: ['0', '1'],
				selectedTags: [],
				commonTags: ['中式黑八', '美式九球', '斯诺克', '英式八球', '花式台球'],
				newTag: '',
				dialogType: 'info'
			}
		},
		onLoad(options) {
			if (options.id) {
				this.id = options.id;
				this.type = options.type || 'view';
				this.loadCoachDetail();
			} else if (options.type === 'add') {
				this.type = 'add';
				this.viewMode = false;
				this.canEdit = true;
				// 初始化默认值
				this.coachInfo = {
					id: 0,
					name: '',
					gender: '男',
					age: '',
					phone: '',
					avatar: '',
					specialty: '',
					experience: '',
					status: '1',
					pricePerHour: '',
					tags: [],
					introduction: ''
				};
			}
			
			// 如果是编辑模式，设置viewMode为false
			if (this.type === 'edit') {
				this.viewMode = false;
			}
		},
		methods: {
			loadCoachDetail() {
				// 从API获取教练详情
				// 这里使用模拟数据
				uni.showLoading({
					title: '加载中'
				});
				
				setTimeout(() => {
					// 模拟数据
					this.coachInfo = {
						id: 1,
						name: '李教练',
						gender: '男',
						age: 32,
						phone: '13800138001',
						avatar: '',
						specialty: '中式台球',
						experience: 8,
						status: '1',
						pricePerHour: '100',
						tags: ['中式黑八', '九球'],
						introduction: '资深台球教练，擅长中式黑八和美式九球教学，曾获得市级台球比赛冠军，教学风格严谨细致，注重基本功培养。'
					};
					
					// 设置状态选择器的索引
					this.statusIndex = this.statusValues.indexOf(this.coachInfo.status);
					if (this.statusIndex === -1) {
						this.statusIndex = 0;
					}
					
					uni.hideLoading();
				}, 500);
			},
			genderChange(e) {
				this.coachInfo.gender = e.detail.value;
			},
			statusChange(e) {
				this.statusIndex = e.detail.value;
				this.coachInfo.status = this.statusValues[this.statusIndex];
			},
			switchToEdit() {
				this.viewMode = false;
				this.type = 'edit';
			},
			toggleTag(tag) {
				if (this.selectedTags.includes(tag)) {
					// 如果已选中，则移除
					const index = this.selectedTags.indexOf(tag);
					this.selectedTags.splice(index, 1);
					
					// 同时从教练标签中移除
					const tagIndex = this.coachInfo.tags.indexOf(tag);
					if (tagIndex !== -1) {
						this.coachInfo.tags.splice(tagIndex, 1);
					}
				} else {
					// 如果未选中，则添加
					this.selectedTags.push(tag);
					
					// 同时添加到教练标签中
					if (!this.coachInfo.tags.includes(tag)) {
						this.coachInfo.tags.push(tag);
					}
				}
			},
			showAddTagDialog() {
				this.newTag = '';
				this.dialogType = 'info';
				this.$refs.tagPopup.open();
			},
			closeDialog() {
				this.$refs.tagPopup.close();
			},
			addNewTag() {
				if (this.newTag.trim()) {
					// 检查是否已存在
					if (!this.coachInfo.tags.includes(this.newTag.trim())) {
						this.coachInfo.tags.push(this.newTag.trim());
						this.selectedTags.push(this.newTag.trim());
					}
				}
				this.closeDialog();
			},
			chooseImage() {
				if (this.viewMode) {
					return;
				}
				
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						this.coachInfo.avatar = res.tempFilePaths[0];
					}
				});
			},
			submitForm() {
				if (this.viewMode) {
					return;
				}
				
				// 表单验证
				if (!this.coachInfo.name) {
					uni.showToast({
						title: '请输入教练姓名',
						icon: 'none'
					});
					return;
				}
				
				if (!this.coachInfo.phone) {
					uni.showToast({
						title: '请输入联系电话',
						icon: 'none'
					});
					return;
				}
				
				// 提交表单
				uni.showLoading({
					title: '保存中'
				});
				
				// 模拟API请求延迟
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
					
					// 保存成功后返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}, 1000);
			},
			goBack() {
				uni.navigateBack();
			},
			goToSchedule() {
				uni.navigateTo({
					url: `/pages/coach/schedule?id=${this.coachInfo.id}&name=${this.coachInfo.name}`
				});
			}
		}
	}
</script>

<style lang="scss">
	.detail-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 150rpx;
	}
	
	.header-card {
		background-color: #FFFFFF;
		padding: 40rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.avatar-box {
		position: relative;
		margin-bottom: 20rpx;
	}
	
	.avatar {
		width: 160rpx;
		height: 160rpx;
		border-radius: 80rpx;
		background-color: #F5F5F5;
	}
	
	.upload-btn {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 50rpx;
		height: 50rpx;
		border-radius: 25rpx;
		background-color: #007AFF;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.base-info {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.coach-name {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
		text-align: center;
	}
	
	.status-tag {
		font-size: 24rpx;
		padding: 4rpx 20rpx;
		border-radius: 20rpx;
	}
	
	.status-working {
		background-color: #EBFFEC;
		color: #00CC66;
	}
	
	.status-rest {
		background-color: #FFF3E0;
		color: #FF9500;
	}
	
	.info-card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		margin: 0 20rpx 20rpx;
		padding: 30rpx;
	}
	
	.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
		border-left: 8rpx solid #007AFF;
		padding-left: 20rpx;
	}
	
	.form-item {
		margin-bottom: 20rpx;
	}
	
	.form-label {
		font-size: 28rpx;
		color: #666666;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.form-content {
		background-color: #F5F5F5;
		padding: 20rpx;
		border-radius: 8rpx;
	}
	
	.form-content input {
		font-size: 28rpx;
		color: #333333;
	}
	
	.radio-group {
		display: flex;
	}
	
	.radio {
		margin-right: 40rpx;
		font-size: 28rpx;
		color: #333333;
		display: flex;
		align-items: center;
	}
	
	.picker-view {
		font-size: 28rpx;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.tags-container {
		display: flex;
		flex-wrap: wrap;
	}
	
	.tag-item {
		padding: 10rpx 20rpx;
		background-color: #F5F5F5;
		color: #666666;
		font-size: 26rpx;
		border-radius: 6rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.tag-item.active {
		background-color: #E3F8FF;
		color: #007AFF;
	}
	
	.add-tag {
		background-color: #F0F7FF;
		color: #007AFF;
		display: flex;
		align-items: center;
	}
	
	.form-textarea {
		width: 100%;
		height: 240rpx;
		background-color: #F5F5F5;
		padding: 20rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #333333;
		box-sizing: border-box;
	}
	
	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #FFFFFF;
		padding: 20rpx;
		display: flex;
		justify-content: space-between;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.action-btn {
		flex: 1;
		margin: 0 10rpx;
		border-radius: 40rpx;
		padding: 20rpx 0;
		font-size: 28rpx;
		text-align: center;
	}
	
	.cancel-btn {
		background-color: #F5F5F5;
		color: #666666;
	}
	
	.primary-btn {
		background-color: #007AFF;
		color: #FFFFFF;
	}
	
	.edit-btn {
		background-color: rgba(0, 122, 255, 0.1);
		color: #007AFF;
	}
	
	.schedule-btn {
		background-color: rgba(0, 204, 102, 0.1);
		color: #00CC66;
	}
	
	.empty-tip {
		font-size: 28rpx;
		color: #999999;
		text-align: center;
		padding: 20rpx 0;
	}
</style> 