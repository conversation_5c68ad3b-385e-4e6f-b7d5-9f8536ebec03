import request from '../../utils/request.js';

// 台桌管理相关API

/**
 * 获取台桌列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} params.status 台桌状态
 * @param {string} params.type 台桌类型
 */
export const getTableList = (params = {}) => {
  return request.get('/merchant/table/list', params);
};

/**
 * 获取台桌详情
 * @param {number} tableId 台桌ID
 */
export const getTableDetail = (tableId) => {
  return request.get(`/merchant/table/detail/${tableId}`);
};

/**
 * 添加台桌
 * @param {Object} data 台桌信息
 * @param {string} data.name 台桌名称
 * @param {string} data.type 台桌类型
 * @param {number} data.hourly_rate 小时费率
 * @param {string} data.description 描述
 * @param {string} data.status 状态
 */
export const addTable = (data) => {
  return request.post('/merchant/table/add', data);
};

/**
 * 更新台桌信息
 * @param {number} tableId 台桌ID
 * @param {Object} data 更新数据
 */
export const updateTable = (tableId, data) => {
  return request.put(`/merchant/table/update/${tableId}`, data);
};

/**
 * 删除台桌
 * @param {number} tableId 台桌ID
 */
export const deleteTable = (tableId) => {
  return request.delete(`/merchant/table/delete/${tableId}`);
};

/**
 * 获取台桌使用统计
 * @param {Object} params 查询参数
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 */
export const getTableStats = (params = {}) => {
  return request.get('/merchant/table/stats', params);
};

/**
 * 设置台桌状态
 * @param {number} tableId 台桌ID
 * @param {string} status 状态 (available/occupied/maintenance)
 */
export const setTableStatus = (tableId, status) => {
  return request.post(`/merchant/table/status/${tableId}`, { status });
}; 