import request from '../../utils/request.js';

// 商家认证相关API

/**
 * 商家注册
 * @param {Object} data 注册数据
 * @param {string} data.name 商家名称
 * @param {string} data.contact_person 联系人
 * @param {string} data.contact_phone 联系电话
 * @param {string} data.address 详细地址
 * @param {string} data.password 登录密码
 * @param {string} data.description 商家描述（可选）
 */
export const merchantRegister = (data) => {
  return request.post('/merchant/auth/register', data);
};

/**
 * 商家登录
 * @param {Object} data 登录数据
 * @param {string} data.phone 手机号
 * @param {string} data.password 密码
 */
export const merchantLogin = (data) => {
  return request.post('/merchant/auth/login', data);
};

/**
 * 获取商家信息
 * 无需参数，通过Token获取当前登录商家信息
 */
export const getMerchantInfo = () => {
  return request.get('/merchant/auth/info');
};

/**
 * 更新商家信息
 * @param {Object} data 更新数据
 * @param {string} data.name 商家名称（可选）
 * @param {string} data.phone 联系电话（可选）
 * @param {string} data.email 邮箱（可选）
 * @param {string} data.address 详细地址（可选）
 * @param {string} data.description 商家描述（可选）
 * @param {string} data.business_hours 营业时间（可选）
 * @param {string} data.avatar 商家头像URL（可选）
 * @param {Array} data.shop_images 店铺介绍图片数组（可选，最多9张）
 */
export const updateMerchantInfo = (data) => {
  return request.post('/merchant/auth/updateInfo', data);
};

/**
 * 商家退出登录
 */
export const merchantLogout = () => {
  return request.post('/merchant/auth/logout');
}; 