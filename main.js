import { createApp } from 'vue'
import uni from './utils/uni-compat'
import UniIcons from './components/uni-icons.vue'

// 将uni挂载到全局
window.uni = uni

// 简单的页面路由管理
const routes = {
  '/pages/login/login': () => import('./pages/login/login.vue'),
  '/pages/login/register': () => import('./pages/login/register.vue'),
  '/pages/index/index': () => import('./pages/index/index.vue'),
  '/pages/appointment/appointment': () => import('./pages/appointment/appointment.vue'),
  '/pages/table/table': () => import('./pages/table/table.vue'),
  '/pages/coach/coach': () => import('./pages/coach/coach.vue'),
  '/pages/mine/mine': () => import('./pages/mine/mine.vue'),
  '/pages/test/api-test': () => import('./pages/test/api-test.vue')
}

// 当前页面组件
let currentComponent = null

// 路由函数
function navigateToPage(path) {
  const route = routes[path]
  if (route) {
    route().then(module => {
      const component = module.default

      // 清除当前内容
      const app = document.getElementById('app')
      app.innerHTML = ''

      // 创建新的Vue应用实例
      const vueApp = createApp(component)
      vueApp.component('uni-icons', UniIcons)
      vueApp.mount('#app')

      currentComponent = vueApp
    })
  }
}

// 监听hash变化
function handleHashChange() {
  const hash = window.location.hash.slice(1) // 移除#
  const path = hash || '/pages/login/login' // 默认到登录页
  navigateToPage(path)
}

// 重写uni的导航方法
uni.navigateTo = function(options) {
  window.location.hash = options.url
}

uni.switchTab = function(options) {
  window.location.hash = options.url
}

uni.reLaunch = function(options) {
  window.location.hash = options.url
}

// 初始化
window.addEventListener('hashchange', handleHashChange)
window.addEventListener('load', handleHashChange)

// 检查登录状态
const token = uni.getStorageSync('merchant_token')
if (!token && !window.location.hash.includes('login')) {
  window.location.hash = '#/pages/login/login'
}