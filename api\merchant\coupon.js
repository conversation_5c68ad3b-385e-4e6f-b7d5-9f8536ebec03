import request from '../../utils/request.js';

// 优惠券管理相关API

/**
 * 获取优惠券列表
 * @param {Object} params 查询参数
 * @param {number} params.merchant_id 商家ID
 * @param {number} params.status 状态：0=禁用 1=启用
 * @param {number} params.type 类型：1=满减券 2=折扣券 3=无门槛券
 * @param {number} params.page 页码，默认1
 * @param {number} params.limit 每页数量，默认10
 */
export const getCouponList = (params = {}) => {
  return request.get('/merchant/coupon/list', params);
};

/**
 * 创建优惠券
 * @param {Object} data 优惠券数据
 * @param {number} data.merchant_id 商家ID
 * @param {string} data.name 优惠券名称
 * @param {number} data.type 类型：1=满减券 2=折扣券 3=无门槛券
 * @param {number} data.discount_value 优惠金额/折扣率
 * @param {number} data.min_amount 最低消费金额
 * @param {number} data.total_quantity 发放总数量
 * @param {string} data.start_time 开始时间
 * @param {string} data.end_time 结束时间
 */
export const createCoupon = (data) => {
  return request.post('/merchant/coupon/create', data);
};

/**
 * 更新优惠券
 * @param {number} id 优惠券ID
 * @param {Object} data 更新数据
 */
export const updateCoupon = (id, data) => {
  return request.put(`/merchant/coupon/update/${id}`, data);
};

/**
 * 删除优惠券
 * @param {number} id 优惠券ID
 */
export const deleteCoupon = (id) => {
  return request.delete(`/merchant/coupon/delete/${id}`);
};

/**
 * 获取优惠券详情
 * @param {number} id 优惠券ID
 */
export const getCouponDetail = (id) => {
  return request.get(`/merchant/coupon/detail/${id}`);
};

/**
 * 启用/禁用优惠券
 * @param {number} id 优惠券ID
 * @param {number} status 状态：0=禁用 1=启用
 */
export const toggleCouponStatus = (id, status) => {
  return request.post(`/merchant/coupon/toggle/${id}`, { status });
};
