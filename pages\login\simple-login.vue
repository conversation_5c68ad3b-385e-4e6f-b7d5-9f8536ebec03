<template>
	<div class="login-container">
		<div class="login-header">
			<img class="logo" src="/logo.png" alt="logo" />
			<span class="title">星界商家助手</span>
			<span class="subtitle">让星界管理更轻松</span>
		</div>
		
		<div class="login-form">
			<div class="form-item">
				<uni-icons type="person" size="24" color="#BDBDBD"></uni-icons>
				<input 
					type="number" 
					v-model="form.phone" 
					placeholder="请输入手机号" 
					maxlength="11" 
					class="input"
				/>
			</div>
			<div class="form-item">
				<uni-icons type="locked" size="24" color="#BDBDBD"></uni-icons>
				<input 
					:type="passwordVisible ? 'text' : 'password'" 
					v-model="form.password" 
					placeholder="请输入密码" 
					class="input"
				/>
				<div class="eye-icon" @click="togglePasswordVisibility">
					<uni-icons :type="passwordVisible ? 'eye' : 'eye-slash'" size="20" color="#BDBDBD"></uni-icons>
				</div>
			</div>
			
			<button class="login-button" @click="handleLogin">登录</button>
			
			<div class="register-link">
				<span>还没有账号？</span>
				<span class="link" @click="navigateToRegister">立即入驻</span>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 响应式数据
const form = ref({
	phone: '',
	password: ''
});
const passwordVisible = ref(false);

// 切换密码可见性
const togglePasswordVisibility = () => {
	passwordVisible.value = !passwordVisible.value;
};

// 导航到注册页面
const navigateToRegister = () => {
	uni.showToast({
		title: '注册功能开发中',
		icon: 'none'
	});
};

// 处理登录逻辑
const handleLogin = () => {
	if (!form.value.phone) {
		uni.showToast({
			title: '请输入手机号',
			icon: 'none'
		});
		return;
	}
	
	if (!form.value.password) {
		uni.showToast({
			title: '请输入密码',
			icon: 'none'
		});
		return;
	}
	
	// 模拟登录成功
	uni.showToast({
		title: '登录成功',
		icon: 'success'
	});
	
	// 跳转到首页
	setTimeout(() => {
		router.push('/index');
	}, 1500);
};
</script>

<style lang="scss">
	.login-container {
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #FFFFFF;
		padding: 30px 20px;
	}
	
	.login-header {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 40px;
		
		.logo {
			width: 90px;
			margin-bottom: 15px;
		}
		
		.title {
			font-size: 18px;
			font-weight: bold;
			color: #333333;
			margin-bottom: 8px;
		}
		
		.subtitle {
			font-size: 14px;
			color: #999999;
		}
	}
	
	.login-form {
		.form-item {
			display: flex;
			align-items: center;
			height: 50px;
			border-bottom: 1px solid #EEEEEE;
			margin-bottom: 15px;
			
			.input {
				flex: 1;
				height: 50px;
				padding: 0 10px;
				font-size: 15px;
				border: none;
				outline: none;
			}
			
			.eye-icon {
				padding: 0 10px;
				cursor: pointer;
			}
		}
		
		.login-button {
			width: 100%;
			height: 45px;
			background-color: #007AFF;
			color: #FFFFFF;
			border-radius: 22px;
			font-size: 16px;
			margin: 25px 0 20px;
			border: none;
			cursor: pointer;
		}
		
		.register-link {
			display: flex;
			justify-content: center;
			margin-bottom: 30px;
			
			span {
				font-size: 14px;
				color: #666666;
			}
			
			.link {
				color: #007AFF;
				margin-left: 4px;
				cursor: pointer;
			}
		}
	}
</style> 