<template>
	<view class="detail-container">
		<!-- 头部状态卡片 -->
		<view class="status-card" :class="'status-'+appointment.status">
			<view class="status-header">
				<text class="status-text">{{getStatusText(appointment.status)}}</text>
				<text class="order-no">订单号: {{appointment.orderNo}}</text>
			</view>
			<view class="status-time">
				<text>{{appointment.createTime}} 创建</text>
			</view>
		</view>
		
		<!-- 预约信息 -->
		<view class="info-card">
			<view class="card-title">预约信息</view>
			<view class="info-content">
				<view class="info-item">
					<text class="label">预约类型</text>
					<text class="value">{{appointment.type === 'coach' ? '教练' : '台球桌'}}</text>
				</view>
				<view class="info-item">
					<text class="label">预约对象</text>
					<text class="value">{{appointment.type === 'coach' ? appointment.coachName : appointment.tableName}}</text>
				</view>
				<view class="info-item">
					<text class="label">预约日期</text>
					<text class="value">{{appointment.appointmentDate}}</text>
				</view>
				<view class="info-item">
					<text class="label">预约时间</text>
					<text class="value">{{appointment.startTime}} - {{appointment.endTime}}</text>
				</view>
				<view class="info-item">
					<text class="label">时长</text>
					<text class="value">{{appointment.duration}}分钟</text>
				</view>
				<view class="info-item">
					<text class="label">金额</text>
					<text class="value price">¥{{appointment.totalAmount}}</text>
				</view>
			</view>
		</view>
		
		<!-- 用户信息 -->
		<view class="info-card">
			<view class="card-title">用户信息</view>
			<view class="info-content">
				<view class="info-item">
					<text class="label">用户姓名</text>
					<text class="value">{{appointment.userName}}</text>
				</view>
				<view class="info-item">
					<text class="label">联系电话</text>
					<text class="value">{{appointment.userPhone}}</text>
				</view>
			</view>
			<view class="card-footer" v-if="appointment.userPhone">
				<button class="call-btn" @click="callUser"><uni-icons type="phone-filled" size="16" color="#007AFF"></uni-icons> 联系用户</button>
			</view>
		</view>
		
		<!-- 备注信息 -->
		<view class="info-card" v-if="appointment.remark || appointment.cancelReason">
			<view class="card-title">备注信息</view>
			<view class="info-content">
				<view class="remark-item" v-if="appointment.remark">
					<text class="remark-label">预约备注</text>
					<text class="remark-content">{{appointment.remark}}</text>
				</view>
				<view class="remark-item" v-if="appointment.cancelReason">
					<text class="remark-label">取消原因</text>
					<text class="remark-content">{{appointment.cancelReason}}</text>
				</view>
			</view>
		</view>
		
		<!-- 底部操作区 -->
		<view class="bottom-actions" v-if="appointment.status === '0' || appointment.status === '1'">
			<button class="action-btn cancel-btn" @click="showCancelDialog" v-if="appointment.status === '0' || appointment.status === '1'">取消预约</button>
			<button class="action-btn confirm-btn" @click="confirmAppointment" v-if="appointment.status === '0'">确认预约</button>
			<button class="action-btn complete-btn" @click="completeAppointment" v-if="appointment.status === '1'">完成预约</button>
		</view>
		
		<!-- 取消预约弹窗 -->
		<uni-popup ref="cancelPopup" type="dialog">
			<uni-popup-dialog
				title="取消预约"
				content="确定要取消该预约吗？取消后将会通知用户。"
				type="error"
				:before-close="true"
				confirmText="确认取消"
				cancelText="关闭"
				@confirm="submitCancelAppointment"
				@close="closeCancelDialog"
			>
				<uni-easyinput
					type="textarea"
					v-model="cancelReason"
					placeholder="请输入取消原因（必填）"
					:maxlength="100"
					style="margin-top: 10px;"
				/>
			</uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: 0,
				appointment: {
					id: 0,
					orderNo: '',
					userName: '',
					userPhone: '',
					type: 'coach', // coach 教练预约，table 台球桌预约
					coachName: '',
					coachId: 0,
					tableName: '',
					tableId: 0,
					appointmentDate: '',
					startTime: '',
					endTime: '',
					duration: 0,
					totalAmount: '0.00',
					status: '0', // 0 待确认，1 已确认，2 已完成，3 已取消
					createTime: '',
					remark: '',
					cancelReason: ''
				},
				cancelReason: ''
			}
		},
		onLoad(options) {
			if (options.id) {
				this.id = options.id;
				this.loadAppointmentDetail();
			}
		},
		methods: {
			loadAppointmentDetail() {
				// 从API获取预约详情
				// 这里使用模拟数据
				// 模拟接口调用延迟
				uni.showLoading({
					title: '加载中'
				});
				
				setTimeout(() => {
					// 根据id查找预约数据
					this.appointment = {
						id: 1,
						orderNo: 'AP2023090101',
						userName: '张先生',
						userPhone: '13800138000',
						type: 'coach', // coach 教练预约，table 台球桌预约
						coachName: '李教练',
						coachId: 1,
						tableName: '',
						tableId: 0,
						appointmentDate: '2023-09-01',
						startTime: '10:00',
						endTime: '12:00',
						duration: 120, // 分钟
						totalAmount: '200.00',
						status: '0', // 0 待确认，1 已确认，2 已完成，3 已取消
						createTime: '2023-09-01 08:30',
						remark: '希望教练能指导黑8的技巧',
						cancelReason: ''
					};
					
					uni.hideLoading();
				}, 500);
			},
			getStatusText(status) {
				const statusMap = {
					'0': '待确认',
					'1': '已确认',
					'2': '已完成',
					'3': '已取消'
				};
				return statusMap[status] || '未知';
			},
			callUser() {
				uni.makePhoneCall({
					phoneNumber: this.appointment.userPhone,
					success: () => {
						console.log('拨打电话成功');
					},
					fail: (err) => {
						console.log('拨打电话失败', err);
					}
				});
			},
			confirmAppointment() {
				uni.showModal({
					title: '确认预约',
					content: '确定接受此预约吗？确认后将会通知用户。',
					success: (res) => {
						if (res.confirm) {
							// 调用API确认预约
							// 这里直接修改本地数据
							this.appointment.status = '1';
							uni.showToast({
								title: '预约已确认',
								icon: 'success'
							});
						}
					}
				});
			},
			completeAppointment() {
				uni.showModal({
					title: '完成预约',
					content: '确定将此预约标记为已完成吗？',
					success: (res) => {
						if (res.confirm) {
							// 调用API完成预约
							// 这里直接修改本地数据
							this.appointment.status = '2';
							uni.showToast({
								title: '预约已完成',
								icon: 'success'
							});
						}
					}
				});
			},
			showCancelDialog() {
				this.cancelReason = '';
				this.$refs.cancelPopup.open();
			},
			closeCancelDialog() {
				this.$refs.cancelPopup.close();
			},
			submitCancelAppointment() {
				if (!this.cancelReason) {
					uni.showToast({
						title: '请输入取消原因',
						icon: 'none'
					});
					return;
				}
				
				// 调用API取消预约
				// 这里直接修改本地数据
				this.appointment.status = '3';
				this.appointment.cancelReason = this.cancelReason;
				
				uni.showToast({
					title: '预约已取消',
					icon: 'success'
				});
				
				this.closeCancelDialog();
			}
		}
	}
</script>

<style lang="scss">
	.detail-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 150rpx;
	}
	
	.status-card {
		padding: 40rpx 30rpx;
		margin-bottom: 20rpx;
	}
	
	.status-0 {
		background-color: #FFF3E0;
	}
	
	.status-1 {
		background-color: #E3F8FF;
	}
	
	.status-2 {
		background-color: #EBFFEC;
	}
	
	.status-3 {
		background-color: #F5F5F5;
	}
	
	.status-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.status-text {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.status-0 .status-text {
		color: #FF9500;
	}
	
	.status-1 .status-text {
		color: #0091FF;
	}
	
	.status-2 .status-text {
		color: #00CC66;
	}
	
	.status-3 .status-text {
		color: #999999;
	}
	
	.order-no {
		font-size: 28rpx;
		color: #666666;
	}
	
	.status-time {
		font-size: 26rpx;
		color: #666666;
	}
	
	.info-card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		margin: 0 20rpx 20rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
		border-left: 8rpx solid #007AFF;
		padding-left: 20rpx;
	}
	
	.info-content {
		margin-bottom: 20rpx;
	}
	
	.info-item {
		display: flex;
		margin-bottom: 15rpx;
	}
	
	.label {
		width: 160rpx;
		font-size: 28rpx;
		color: #999999;
	}
	
	.value {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
	}
	
	.price {
		color: #FF3B30;
		font-weight: bold;
	}
	
	.remark-item {
		margin-bottom: 15rpx;
	}
	
	.remark-label {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 10rpx;
		display: block;
	}
	
	.remark-content {
		font-size: 28rpx;
		color: #333333;
		background-color: #F5F5F5;
		padding: 20rpx;
		border-radius: 8rpx;
		display: block;
	}
	
	.card-footer {
		border-top: 1rpx solid #F5F5F5;
		padding-top: 20rpx;
		display: flex;
		justify-content: center;
	}
	
	.call-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #F0F7FF;
		color: #007AFF;
		font-size: 28rpx;
		border-radius: 40rpx;
		padding: 15rpx 40rpx;
		border: none;
	}
	
	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #FFFFFF;
		padding: 20rpx;
		display: flex;
		justify-content: flex-end;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.action-btn {
		margin-left: 20rpx;
		border-radius: 40rpx;
		padding: 15rpx 30rpx;
		font-size: 28rpx;
		border: none;
	}
	
	.cancel-btn {
		background-color: rgba(255, 59, 48, 0.1);
		color: #FF3B30;
	}
	
	.confirm-btn {
		background-color: rgba(0, 204, 102, 0.1);
		color: #00CC66;
	}
	
	.complete-btn {
		background-color: rgba(0, 122, 255, 0.1);
		color: #007AFF;
	}
</style> 