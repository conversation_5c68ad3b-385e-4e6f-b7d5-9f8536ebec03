import request from '../../utils/request.js';

// 数据统计相关API

/**
 * 获取首页概览数据
 * @param {Object} params 查询参数
 * @param {string} params.date 日期 (可选，默认今天)
 */
export const getDashboardOverview = (params = {}) => {
  return request.get('/merchant/stats/dashboard', params);
};

/**
 * 获取收入统计
 * @param {Object} params 查询参数
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 * @param {string} params.type 统计类型 (daily/weekly/monthly)
 */
export const getRevenueStats = (params = {}) => {
  return request.get('/merchant/stats/revenue', params);
};

/**
 * 获取预订统计
 * @param {Object} params 查询参数
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 * @param {string} params.type 统计类型 (daily/weekly/monthly)
 */
export const getBookingStats = (params = {}) => {
  return request.get('/merchant/stats/booking', params);
};

/**
 * 获取台桌使用率统计
 * @param {Object} params 查询参数
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 */
export const getTableUtilizationStats = (params = {}) => {
  return request.get('/merchant/stats/table-utilization', params);
};

/**
 * 获取教练绩效统计
 * @param {Object} params 查询参数
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 */
export const getCoachPerformanceStats = (params = {}) => {
  return request.get('/merchant/stats/coach-performance', params);
};

/**
 * 获取用户分析数据
 * @param {Object} params 查询参数
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 */
export const getUserAnalytics = (params = {}) => {
  return request.get('/merchant/stats/user-analytics', params);
};

/**
 * 获取热门时段统计
 * @param {Object} params 查询参数
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 */
export const getPeakHoursStats = (params = {}) => {
  return request.get('/merchant/stats/peak-hours', params);
};

/**
 * 导出统计报表
 * @param {Object} params 导出参数
 * @param {string} params.type 报表类型 (revenue/booking/table/coach)
 * @param {string} params.start_date 开始日期
 * @param {string} params.end_date 结束日期
 * @param {string} params.format 导出格式 (excel/pdf)
 */
export const exportStatsReport = (params = {}) => {
  return request.get('/merchant/stats/export', params);
}; 