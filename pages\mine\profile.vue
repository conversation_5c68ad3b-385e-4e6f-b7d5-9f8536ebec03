<template>
	<view class="profile-container">
		<form @submit="submitForm">
			<!-- 标题部分 -->
			<view class="section-title">基本信息</view>
			
			<!-- 商家Logo -->
			<view class="form-block">
				<view class="avatar-item">
					<text class="label">商家Logo</text>
					<view class="avatar-content">
						<view class="avatar-box">
							<image class="avatar" :src="merchantInfo.logoUrl || '/static/images/default-logo.png'" mode="aspectFill"></image>
							<view class="upload-btn" @click="chooseImage">
								<uni-icons type="camera" size="20" color="#FFFFFF"></uni-icons>
							</view>
						</view>
						<text class="avatar-tips">点击更换商家Logo</text>
					</view>
				</view>
			</view>
			
			<!-- 基础信息表单 -->
			<view class="form-block">
				<view class="form-item">
					<text class="label">商家名称</text>
					<view class="input-box">
						<input type="text" v-model="merchantInfo.merchantName" placeholder="请输入商家名称"/>
					</view>
				</view>
				<view class="form-item">
					<text class="label">联系电话</text>
					<view class="input-box">
						<input type="text" v-model="merchantInfo.contactPhone" placeholder="请输入联系电话"/>
					</view>
				</view>
				<view class="form-item">
					<text class="label">商家类型</text>
					<view class="input-box">
						<picker @change="typeChange" :value="typeIndex" :range="typeOptions">
							<view class="picker-view">
								<text v-if="typeIndex !== ''">{{typeOptions[typeIndex]}}</text>
								<text v-else class="placeholder">请选择商家类型</text>
								<uni-icons type="bottom" size="14" color="#999999"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
				<view class="form-item">
					<text class="label">营业时间</text>
					<view class="input-box">
						<view class="time-range-picker" @click="navigateTo('/pages/merchant/business-hours')">
							<text v-if="merchantInfo.businessHours">{{merchantInfo.businessHours}}</text>
							<text v-else class="placeholder">请设置营业时间</text>
							<uni-icons type="right" size="14" color="#999999"></uni-icons>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 地址信息 -->
			<view class="section-title">地址信息</view>
			<view class="form-block">
				<view class="form-item">
					<text class="label">所在城市</text>
					<view class="input-box">
						<picker mode="region" @change="regionChange" :value="selectedRegion">
							<view class="picker-view">
								<text v-if="selectedRegion.length === 3">{{selectedRegion[0]}} {{selectedRegion[1]}} {{selectedRegion[2]}}</text>
								<text v-else class="placeholder">请选择所在城市</text>
								<uni-icons type="bottom" size="14" color="#999999"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
				<view class="form-item">
					<text class="label">详细地址</text>
					<view class="input-box">
						<input type="text" v-model="merchantInfo.addressDetail" placeholder="请输入详细地址"/>
					</view>
				</view>
				<view class="form-item">
					<text class="label">地址位置</text>
					<view class="map-box" @click="chooseLocation">
						<map class="map" :latitude="merchantInfo.latitude" :longitude="merchantInfo.longitude" :markers="markers" v-if="merchantInfo.latitude && merchantInfo.longitude"></map>
						<view class="map-placeholder" v-else>
							<uni-icons type="location" size="30" color="#999999"></uni-icons>
							<text class="map-placeholder-text">点击选择位置</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 商家介绍 -->
			<view class="section-title">商家介绍</view>
			<view class="form-block">
				<view class="form-item">
					<text class="label">商家介绍</text>
					<view class="input-box">
						<textarea class="textarea" v-model="merchantInfo.description" placeholder="请输入商家介绍，如球馆特色、设施条件等" />
					</view>
				</view>
				<view class="form-item">
					<text class="label">店内照片</text>
					<view class="photos-box">
						<view class="photo-list">
							<view class="photo-item" v-for="(photo, index) in merchantInfo.photos" :key="index">
								<image class="photo" :src="photo" mode="aspectFill"></image>
								<view class="delete-btn" @click="deletePhoto(index)">
									<uni-icons type="closeempty" size="14" color="#FFFFFF"></uni-icons>
								</view>
							</view>
							<view class="photo-item add-photo" @click="choosePhotos" v-if="merchantInfo.photos.length < 9">
								<uni-icons type="plusempty" size="30" color="#999999"></uni-icons>
							</view>
						</view>
						<text class="photo-tips">最多可上传9张照片</text>
					</view>
				</view>
			</view>
			
			<!-- 按钮区域 -->
			<view class="bottom-actions">
				<button class="action-btn cancel-btn" @click="goBack">取消</button>
				<button class="action-btn primary-btn" form-type="submit">保存</button>
			</view>
		</form>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				merchantInfo: {
					merchantId: '',
					merchantName: '',
					logoUrl: '',
					contactPerson: '',
					contactPhone: '',
					address: '',
					addressDetail: '',
					description: '',
					latitude: null,
					longitude: null,
					tableCount: 0,
					businessHours: '10:00-22:00',
					photos: []
				},
				typeIndex: 0,
				typeOptions: ['标准星界', '综合球馆', '俱乐部'],
				typeValues: ['1', '2', '3'],
				selectedRegion: ['广东省', '广州市', '天河区'],
				markers: [],
				token: ''
			}
		},
		onLoad() {
			// 获取token
			this.token = uni.getStorageSync('merchantToken');
			if (!this.token) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages/login/login'
					});
				}, 1500);
				return;
			}
			
			// 加载商家信息
			this.loadMerchantInfo();
		},
		methods: {
			loadMerchantInfo() {
				// 从API获取商家信息
				uni.showLoading({
					title: '加载中'
				});
				
				uni.request({
					url: 'http://localhost:8080/merchant/info',
					method: 'GET',
					data: {
						token: this.token
					},
					success: (res) => {
						uni.hideLoading();
						
						if (res.data.code === 200) {
							// 设置商家信息
							this.merchantInfo = res.data.data;
							
							// 如果有经纬度，设置地图标记
							if (this.merchantInfo.latitude && this.merchantInfo.longitude) {
								this.setMapMarker();
							}
							
							// 设置商家类型索引
							this.typeIndex = this.typeValues.indexOf(this.merchantInfo.type || '1');
							if (this.typeIndex === -1) {
								this.typeIndex = 0;
							}
							
							// 处理地址
							if (this.merchantInfo.address) {
								const addressParts = this.merchantInfo.address.split(' ');
								if (addressParts.length >= 3) {
									this.selectedRegion = [addressParts[0], addressParts[1], addressParts[2]];
								}
							}
							
							// 如果没有图片，初始化为空数组
							if (!this.merchantInfo.photos) {
								this.merchantInfo.photos = [];
							} else if (typeof this.merchantInfo.photos === 'string') {
								// 如果是字符串，转为数组
								this.merchantInfo.photos = this.merchantInfo.photos.split(',').filter(item => item);
							}
						} else {
							uni.showToast({
								title: res.data.msg || '获取商家信息失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						uni.hideLoading();
						
						uni.showToast({
							title: '网络请求失败，请检查网络连接',
							icon: 'none'
						});
						
						console.error('获取商家信息失败', err);
					}
				});
			},
			chooseImage() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						
						// 上传图片
						this.uploadFile(tempFilePath, (url) => {
							this.merchantInfo.logoUrl = url;
						});
					}
				});
			},
			choosePhotos() {
				const maxCount = 9 - this.merchantInfo.photos.length;
				if (maxCount <= 0) {
					uni.showToast({
						title: '最多上传9张照片',
						icon: 'none'
					});
					return;
				}
				
				uni.chooseImage({
					count: maxCount,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 上传所有图片
						const tempFilePaths = res.tempFilePaths;
						
						tempFilePaths.forEach(path => {
							this.uploadFile(path, (url) => {
								this.merchantInfo.photos.push(url);
							});
						});
					}
				});
			},
			// 上传文件方法
			uploadFile(filePath, callback) {
				uni.showLoading({
					title: '上传中...'
				});
				
				uni.uploadFile({
					url: 'http://localhost:8080/merchant/upload',
					filePath: filePath,
					name: 'file',
					success: (uploadRes) => {
						uni.hideLoading();
						
						const result = JSON.parse(uploadRes.data);
						if (result.code === 200) {
							// 上传成功，获取图片URL
							const url = result.data.url;
							
							if (callback) {
								callback(url);
							}
							
							uni.showToast({
								title: '上传成功',
								icon: 'success'
							});
						} else {
							uni.showToast({
								title: result.msg || '上传失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						uni.hideLoading();
						
						uni.showToast({
							title: '上传失败，请重试',
							icon: 'none'
						});
						
						console.error('上传文件失败', err);
					}
				});
			},
			deletePhoto(index) {
				this.merchantInfo.photos.splice(index, 1);
			},
			typeChange(e) {
				this.typeIndex = e.detail.value;
				this.merchantInfo.type = this.typeValues[this.typeIndex];
			},
			regionChange(e) {
				this.selectedRegion = e.detail.value;
				
				// 更新地址信息
				this.merchantInfo.address = this.selectedRegion.join(' ');
			},
			chooseLocation() {
				uni.chooseLocation({
					success: (res) => {
						this.merchantInfo.latitude = res.latitude;
						this.merchantInfo.longitude = res.longitude;
						this.merchantInfo.address = res.address;
						this.setMapMarker();
					}
				});
			},
			setMapMarker() {
				this.markers = [{
					id: 1,
					latitude: this.merchantInfo.latitude,
					longitude: this.merchantInfo.longitude,
					iconPath: '/static/images/marker.png',
					width: 30,
					height: 30
				}];
			},
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			submitForm() {
				// 表单验证
				if (!this.merchantInfo.merchantName) {
					uni.showToast({
						title: '请输入商家名称',
						icon: 'none'
					});
					return;
				}
				
				if (!this.merchantInfo.contactPhone) {
					uni.showToast({
						title: '请输入联系电话',
						icon: 'none'
					});
					return;
				}
				
				if (!this.merchantInfo.address) {
					uni.showToast({
						title: '请选择商家地址',
						icon: 'none'
					});
					return;
				}
				
				// 将照片数组转为字符串
				if (Array.isArray(this.merchantInfo.photos)) {
					this.merchantInfo.photos = this.merchantInfo.photos.join(',');
				}
				
				// 提交表单
				uni.showLoading({
					title: '保存中'
				});
				
				uni.request({
					url: 'http://localhost:8080/merchant/update',
					method: 'POST',
					header: {
						'content-type': 'application/json'
					},
					data: {
						token: this.token,
						...this.merchantInfo
					},
					success: (res) => {
						uni.hideLoading();
						
						if (res.data.code === 200) {
							uni.showToast({
								title: '保存成功',
								icon: 'success'
							});
							
							// 保存成功后返回上一页
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						} else {
							uni.showToast({
								title: res.data.msg || '保存失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						uni.hideLoading();
						
						uni.showToast({
							title: '网络请求失败，请检查网络连接',
							icon: 'none'
						});
						
						console.error('更新商家信息失败', err);
					}
				});
			},
			goBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style lang="scss">
	.profile-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 150rpx;
	}
	
	.section-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		padding: 30rpx 30rpx 20rpx;
	}
	
	.form-block {
		background-color: #FFFFFF;
		margin-bottom: 20rpx;
	}
	
	.form-item {
		padding: 30rpx;
		position: relative;
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			left: 30rpx;
			right: 30rpx;
			bottom: 0;
			height: 1rpx;
			background-color: #F5F5F5;
		}
	}
	
	.avatar-item {
		display: flex;
		padding: 30rpx;
	}
	
	.label {
		font-size: 28rpx;
		color: #333333;
		width: 160rpx;
		line-height: 80rpx;
	}
	
	.avatar-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.avatar-box {
		position: relative;
		margin-bottom: 10rpx;
	}
	
	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		background-color: #F5F5F5;
	}
	
	.upload-btn {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 40rpx;
		height: 40rpx;
		border-radius: 20rpx;
		background-color: #007AFF;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.avatar-tips {
		font-size: 24rpx;
		color: #999999;
	}
	
	.input-box {
		flex: 1;
	}
	
	.input-box input {
		height: 80rpx;
		font-size: 28rpx;
		color: #333333;
	}
	
	.picker-view {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		color: #333333;
	}
	
	.time-range-picker {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		color: #333333;
	}
	
	.placeholder {
		color: #999999;
	}
	
	.map-box {
		height: 300rpx;
		margin-top: 20rpx;
		border-radius: 8rpx;
		overflow: hidden;
	}
	
	.map {
		width: 100%;
		height: 100%;
	}
	
	.map-placeholder {
		width: 100%;
		height: 100%;
		background-color: #F5F5F5;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}
	
	.map-placeholder-text {
		font-size: 28rpx;
		color: #999999;
		margin-top: 10rpx;
	}
	
	.textarea {
		width: 100%;
		height: 240rpx;
		font-size: 28rpx;
		color: #333333;
	}
	
	.photos-box {
		margin-top: 20rpx;
	}
	
	.photo-list {
		display: flex;
		flex-wrap: wrap;
	}
	
	.photo-item {
		width: 200rpx;
		height: 200rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		position: relative;
	}
	
	.photo {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
	}
	
	.delete-btn {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
		width: 40rpx;
		height: 40rpx;
		border-radius: 20rpx;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.add-photo {
		background-color: #F5F5F5;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.photo-tips {
		font-size: 24rpx;
		color: #999999;
		margin-top: 10rpx;
	}
	
	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #FFFFFF;
		padding: 20rpx;
		display: flex;
		justify-content: space-between;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.action-btn {
		flex: 1;
		margin: 0 10rpx;
		border-radius: 40rpx;
		padding: 20rpx 0;
		font-size: 28rpx;
		text-align: center;
	}
	
	.cancel-btn {
		background-color: #F5F5F5;
		color: #666666;
	}
	
	.primary-btn {
		background-color: #007AFF;
		color: #FFFFFF;
	}
</style> 