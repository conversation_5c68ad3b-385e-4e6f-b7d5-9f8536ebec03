<template>
	<view class="test-container">
		<view class="test-header">
			<text class="test-title">API连接测试</text>
		</view>
		
		<view class="test-section">
			<text class="section-title">配置信息</text>
			<view class="config-item">
				<text class="config-label">API地址:</text>
				<text class="config-value">{{apiBaseURL}}</text>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">登录测试</text>
			<view class="form-group">
				<input v-model="loginForm.phone" placeholder="手机号" class="test-input" />
				<input v-model="loginForm.password" placeholder="密码" type="password" class="test-input" />
				<button @click="testLogin" class="test-btn">测试登录</button>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">注册测试</text>
			<view class="form-group">
				<input v-model="registerForm.name" placeholder="商家名称" class="test-input" />
				<input v-model="registerForm.contact_person" placeholder="联系人" class="test-input" />
				<input v-model="registerForm.contact_phone" placeholder="联系电话" class="test-input" />
				<input v-model="registerForm.password" placeholder="密码" type="password" class="test-input" />
				<button @click="testRegister" class="test-btn">测试注册</button>
			</view>
		</view>
		
		<view class="test-section">
			<text class="section-title">测试结果</text>
			<view class="result-box">
				<text class="result-text">{{testResult}}</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref } from 'vue';
import { merchantLogin, merchantRegister } from '../../api/merchant/auth.js';
import config from '../../config/index.js';

// 响应式数据
const apiBaseURL = ref(config.api.baseURL);
const testResult = ref('等待测试...');

const loginForm = ref({
	phone: '13800138000',
	password: '123456'
});

const registerForm = ref({
	name: '测试台球厅',
	contact_person: '测试联系人',
	contact_phone: '13900139000',
	password: '123456',
	address: '测试地址',
	description: '测试描述'
});

// 测试登录
const testLogin = async () => {
	try {
		testResult.value = '正在测试登录...';
		
		const response = await merchantLogin(loginForm.value);
		
		testResult.value = `登录测试结果:\n${JSON.stringify(response, null, 2)}`;
		
		if (response.code === '0000') {
			uni.showToast({
				title: '登录测试成功',
				icon: 'success'
			});
		} else {
			uni.showToast({
				title: '登录测试失败',
				icon: 'none'
			});
		}
	} catch (error) {
		testResult.value = `登录测试错误:\n${error.message || error}`;
		uni.showToast({
			title: '登录测试异常',
			icon: 'none'
		});
	}
};

// 测试注册
const testRegister = async () => {
	try {
		testResult.value = '正在测试注册...';
		
		const response = await merchantRegister(registerForm.value);
		
		testResult.value = `注册测试结果:\n${JSON.stringify(response, null, 2)}`;
		
		if (response.code === '0000') {
			uni.showToast({
				title: '注册测试成功',
				icon: 'success'
			});
		} else {
			uni.showToast({
				title: '注册测试失败',
				icon: 'none'
			});
		}
	} catch (error) {
		testResult.value = `注册测试错误:\n${error.message || error}`;
		uni.showToast({
			title: '注册测试异常',
			icon: 'none'
		});
	}
};
</script>

<style lang="scss">
.test-container {
	padding: 20rpx;
	background-color: #F5F5F5;
	min-height: 100vh;
}

.test-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.test-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333333;
}

.test-section {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 20rpx;
	display: block;
}

.config-item {
	display: flex;
	margin-bottom: 10rpx;
}

.config-label {
	font-size: 28rpx;
	color: #666666;
	width: 150rpx;
}

.config-value {
	font-size: 28rpx;
	color: #333333;
	flex: 1;
	word-break: break-all;
}

.form-group {
	display: flex;
	flex-direction: column;
}

.test-input {
	height: 80rpx;
	padding: 0 20rpx;
	border: 2rpx solid #EEEEEE;
	border-radius: 8rpx;
	font-size: 28rpx;
	margin-bottom: 20rpx;
	background-color: #FFFFFF;
}

.test-btn {
	height: 80rpx;
	background-color: #007AFF;
	color: #FFFFFF;
	border-radius: 8rpx;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
}

.result-box {
	background-color: #F8F8F8;
	border-radius: 8rpx;
	padding: 20rpx;
	min-height: 200rpx;
}

.result-text {
	font-size: 24rpx;
	color: #333333;
	white-space: pre-wrap;
	word-break: break-all;
}
</style>
