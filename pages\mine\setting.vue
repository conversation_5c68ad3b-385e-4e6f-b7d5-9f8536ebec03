<template>
	<view class="setting-container">
		<!-- 设置列表 -->
		<view class="setting-list">
			<!-- 账号安全设置 -->
			<view class="setting-group">
				<view class="setting-title">账号设置</view>
				<view class="setting-item" @click="navigateTo('/pages/mine/account-security')">
					<text class="item-name">账号安全</text>
					<view class="item-right">
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
				</view>
				<view class="setting-item" @click="navigateTo('/pages/mine/change-password')">
					<text class="item-name">修改密码</text>
					<view class="item-right">
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
				</view>
			</view>
			
			<!-- 消息通知 -->
			<view class="setting-group">
				<view class="setting-title">消息通知</view>
				<view class="setting-item">
					<text class="item-name">新订单通知</text>
					<view class="item-right">
						<switch :checked="notificationSettings.newOrder" @change="toggleSetting('newOrder')" color="#007AFF" />
					</view>
				</view>
				<view class="setting-item">
					<text class="item-name">系统通知</text>
					<view class="item-right">
						<switch :checked="notificationSettings.system" @change="toggleSetting('system')" color="#007AFF" />
					</view>
				</view>
				<view class="setting-item">
					<text class="item-name">活动推送</text>
					<view class="item-right">
						<switch :checked="notificationSettings.promotion" @change="toggleSetting('promotion')" color="#007AFF" />
					</view>
				</view>
			</view>
			
			<!-- 通用设置 -->
			<view class="setting-group">
				<view class="setting-title">通用设置</view>
				<view class="setting-item" @click="clearCache">
					<text class="item-name">清除缓存</text>
					<view class="item-right">
						<text class="cache-size">{{cacheSize}}</text>
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
				</view>
				<view class="setting-item" @click="navigateTo('/pages/mine/privacy')">
					<text class="item-name">隐私协议</text>
					<view class="item-right">
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
				</view>
				<view class="setting-item" @click="navigateTo('/pages/mine/about')">
					<text class="item-name">关于我们</text>
					<view class="item-right">
						<text class="version">v{{appVersion}}</text>
						<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
					</view>
				</view>
			</view>
			
			<!-- 退出登录 -->
			<view class="logout-button" @click="showLogoutConfirm">
				退出登录
			</view>
		</view>
		
		<!-- 退出登录确认弹窗 -->
		<uni-popup ref="logoutPopup" type="dialog">
			<uni-popup-dialog
				type="warning"
				title="退出登录"
				content="确定要退出当前账号吗？"
				:before-close="true"
				@confirm="logout"
				@close="cancelLogout"
			></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				notificationSettings: {
					newOrder: true,
					system: true,
					promotion: false
				},
				cacheSize: '12.5MB',
				appVersion: '1.0.0'
			}
		},
		onLoad() {
			// 加载设置信息
			this.loadSettings();
		},
		methods: {
			loadSettings() {
				// 从本地存储加载设置信息
				const settings = uni.getStorageSync('notificationSettings');
				if (settings) {
					this.notificationSettings = JSON.parse(settings);
				}
				
				// 获取缓存大小
				this.getCacheSize();
				
				// 获取App版本
				this.getAppVersion();
			},
			toggleSetting(key) {
				this.notificationSettings[key] = !this.notificationSettings[key];
				// 保存设置到本地存储
				uni.setStorageSync('notificationSettings', JSON.stringify(this.notificationSettings));
				
				// 显示提示
				uni.showToast({
					title: this.notificationSettings[key] ? '已开启' : '已关闭',
					icon: 'none'
				});
			},
			getCacheSize() {
				// 获取缓存大小的方法，这里使用模拟数据
				// 实际开发中可以使用uni.getSavedFileList等API获取
			},
			getAppVersion() {
				// 获取App版本的方法
				// 在实际开发中可以使用plus.runtime.version获取
			},
			clearCache() {
				uni.showLoading({
					title: '清理中'
				});
				
				// 模拟清理过程
				setTimeout(() => {
					uni.hideLoading();
					this.cacheSize = '0KB';
					
					uni.showToast({
						title: '缓存清理成功',
						icon: 'success'
					});
				}, 1000);
			},
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			showLogoutConfirm() {
				this.$refs.logoutPopup.open();
			},
			logout() {
				// 执行退出登录操作
				uni.showLoading({
					title: '退出中'
				});
				
				// 清除本地存储的用户信息
				uni.removeStorageSync('userInfo');
				uni.removeStorageSync('token');
				
				setTimeout(() => {
					uni.hideLoading();
					
					// 跳转到登录页
					uni.reLaunch({
						url: '/pages/login/login'
					});
				}, 1000);
			},
			cancelLogout() {
				this.$refs.logoutPopup.close();
			}
		}
	}
</script>

<style lang="scss">
	.setting-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 30rpx;
	}
	
	.setting-list {
		padding: 20rpx;
	}
	
	.setting-group {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.setting-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #F5F5F5;
	}
	
	.setting-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		position: relative;
		
		&:not(:last-child)::after {
			content: '';
			position: absolute;
			left: 30rpx;
			right: 30rpx;
			bottom: 0;
			height: 1rpx;
			background-color: #F5F5F5;
		}
	}
	
	.item-name {
		font-size: 28rpx;
		color: #333333;
	}
	
	.item-right {
		display: flex;
		align-items: center;
	}
	
	.cache-size {
		font-size: 24rpx;
		color: #999999;
		margin-right: 10rpx;
	}
	
	.version {
		font-size: 24rpx;
		color: #999999;
		margin-right: 10rpx;
	}
	
	.logout-button {
		background-color: #FFFFFF;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #FF3B30;
		border-radius: 16rpx;
		margin-top: 60rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
</style> 