<template>
	<view class="coach-container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input-box">
				<uni-icons type="search" size="18" color="#999"></uni-icons>
				<input class="search-input" type="text" v-model="searchKey" placeholder="搜索教练姓名" @confirm="searchCoach"/>
			</view>
			<button class="add-btn" @click="navigateTo('/pages/coach/detail?type=add')">添加</button>
		</view>
		
		<!-- 过滤栏 -->
		<view class="filter-bar">
			<view class="filter-item" :class="{active: activeFilter === 'all'}" @click="filterCoaches('all')">
				全部
			</view>
			<view class="filter-item" :class="{active: activeFilter === 'working'}" @click="filterCoaches('working')">
				工作中
			</view>
			<view class="filter-item" :class="{active: activeFilter === 'rest'}" @click="filterCoaches('rest')">
				休息中
			</view>
		</view>
		
		<!-- 教练列表 -->
		<view class="coach-list">
			<view class="coach-item" v-for="(coach, index) in filteredCoaches" :key="index" @click="navigateTo('/pages/coach/detail?id='+coach.id)">
				<view class="coach-avatar-container">
					<image class="coach-avatar" :src="coach.avatar || '/static/images/default-coach.png'" mode="aspectFill"></image>
					<view class="coach-status" :class="coach.status === '1' ? 'status-working' : 'status-rest'"></view>
				</view>
				<view class="coach-info">
					<view class="coach-header">
						<text class="coach-name">{{coach.name}}</text>
						<text class="coach-specialty">{{coach.specialty}}</text>
					</view>
					<view class="coach-detail">
						<view class="detail-item">
							<uni-icons type="phone" size="14" color="#666"></uni-icons>
							<text class="detail-text">{{coach.phone}}</text>
						</view>
						<view class="detail-item">
							<uni-icons type="calendar" size="14" color="#666"></uni-icons>
							<text class="detail-text">{{coach.experience}}年教龄</text>
						</view>
					</view>
					<view class="coach-tags">
						<text class="tag" v-for="(tag, tagIndex) in coach.tags" :key="tagIndex">{{tag}}</text>
					</view>
				</view>
				<view class="coach-actions">
					<view class="action-btn" @click.stop="callCoach(coach)">
						<uni-icons type="phone-filled" size="20" color="#007AFF"></uni-icons>
					</view>
					<view class="action-btn" @click.stop="navigateTo('/pages/coach/schedule?id='+coach.id)">
						<uni-icons type="calendar" size="20" color="#007AFF"></uni-icons>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-tip" v-if="filteredCoaches.length === 0">
				<image class="empty-icon" src="/static/images/empty.png"></image>
				<text class="empty-text">暂无教练数据</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { getCoachList } from '../../api/merchant/coach.js';

// 响应式数据
const searchKey = ref('');
const activeFilter = ref('all');
const coaches = ref([
	{
		id: 1,
		name: '李教练',
		gender: '男',
		age: 32,
		phone: '13800138001',
		avatar: '',
		specialty: '中式台球',
		experience: 8,
		status: '1', // 1: 工作中，0: 休息中
		tags: ['中式黑八', '九球']
	},
	{
		id: 2,
		name: '王教练',
		gender: '女',
		age: 28,
		phone: '13800138002',
		avatar: '',
		specialty: '美式台球',
		experience: 5,
		status: '0',
		tags: ['斯诺克', '九球']
	},
	{
		id: 3,
		name: '张教练',
		gender: '男',
		age: 35,
		phone: '13800138003',
		avatar: '',
		specialty: '斯诺克',
		experience: 10,
		status: '1',
		tags: ['斯诺克', '中式黑八']
	}
]);

// 计算属性
const filteredCoaches = computed(() => {
	let result = [...coaches.value];
	
	// 按搜索关键词过滤
	if (searchKey.value) {
		result = result.filter(coach => coach.name.includes(searchKey.value));
	}
	
	// 按状态过滤
	if (activeFilter.value === 'working') {
		result = result.filter(coach => coach.status === '1');
	} else if (activeFilter.value === 'rest') {
		result = result.filter(coach => coach.status === '0');
	}
	
	return result;
});

// 页面加载时
onMounted(() => {
	loadCoaches();
});

// 加载教练数据
const loadCoaches = async () => {
	try {
		uni.showLoading({
			title: '加载中...'
		});

		const response = await getCoachList({
			page: 1,
			pageSize: 100, // 获取所有教练
			search: searchKey.value,
			status: activeFilter.value === 'all' ? '' : getStatusValueByFilter(activeFilter.value)
		});

		uni.hideLoading();

		if (response.code === 200) {
			coaches.value = response.data.list.map(coach => ({
				id: coach.id,
				name: coach.name,
				gender: coach.gender,
				age: coach.age,
				phone: coach.phone,
				avatar: coach.avatar,
				specialty: coach.specialty,
				experience: coach.experience,
				status: coach.status,
				tags: coach.tags ? coach.tags.split(',') : []
			}));
		} else {
			uni.showToast({
				title: response.msg || '获取教练列表失败',
				icon: 'none'
			});
		}
	} catch (error) {
		uni.hideLoading();
		console.error('获取教练列表失败', error);
		uni.showToast({
			title: '网络错误，请稍后重试',
			icon: 'none'
		});
	}
};

// 根据过滤器获取状态值
const getStatusValueByFilter = (filter) => {
	const filterMap = {
		'working': '1',
		'rest': '0'
	};
	return filterMap[filter] || '';
};

// 搜索教练
const searchCoach = () => {
	// 重新加载数据
	loadCoaches();
};

// 过滤教练
const filterCoaches = (type) => {
	activeFilter.value = type;
	// 重新加载数据
	loadCoaches();
};

// 导航到指定页面
const navigateTo = (url) => {
	uni.navigateTo({
		url: url
	});
};

// 拨打电话
const callCoach = (coach) => {
	uni.makePhoneCall({
		phoneNumber: coach.phone,
		success: () => {
			console.log('拨打电话成功');
		},
		fail: (err) => {
			console.log('拨打电话失败', err);
		}
	});
};
</script>

<style lang="scss">
	.coach-container {
		min-height: 100vh;
		background-color: #F5F5F5;
		padding-bottom: 30rpx;
	}
	
	.search-bar {
		padding: 20rpx;
		background-color: #FFFFFF;
		display: flex;
		align-items: center;
	}
	
	.search-input-box {
		flex: 1;
		height: 72rpx;
		background-color: #F5F5F5;
		border-radius: 36rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		margin-right: 20rpx;
	}
	
	.search-input {
		flex: 1;
		height: 72rpx;
		font-size: 28rpx;
		margin-left: 10rpx;
	}
	
	.add-btn {
		width: 160rpx;
		height: 72rpx;
		background-color: #007AFF;
		color: #FFFFFF;
		font-size: 28rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 36rpx;
		padding: 0;
	}
	
	.filter-bar {
		display: flex;
		background-color: #FFFFFF;
		padding: 0 20rpx;
		margin-bottom: 20rpx;
		border-bottom: 1rpx solid #EEEEEE;
	}
	
	.filter-item {
		padding: 20rpx 30rpx;
		font-size: 28rpx;
		color: #666666;
		position: relative;
	}
	
	.filter-item.active {
		color: #007AFF;
		font-weight: bold;
		
		&::after {
			content: '';
			position: absolute;
			left: 30rpx;
			right: 30rpx;
			bottom: 0;
			height: 4rpx;
			background-color: #007AFF;
			border-radius: 2rpx;
		}
	}
	
	.coach-list {
		padding: 0 20rpx;
	}
	
	.coach-item {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		display: flex;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.coach-avatar-container {
		position: relative;
		margin-right: 20rpx;
	}
	
	.coach-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		background-color: #F5F5F5;
	}
	
	.coach-status {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 24rpx;
		height: 24rpx;
		border-radius: 12rpx;
		border: 4rpx solid #FFFFFF;
	}
	
	.status-working {
		background-color: #00CC66;
	}
	
	.status-rest {
		background-color: #FF9500;
	}
	
	.coach-info {
		flex: 1;
	}
	
	.coach-header {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.coach-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-right: 20rpx;
	}
	
	.coach-specialty {
		font-size: 24rpx;
		color: #007AFF;
		background-color: rgba(0, 122, 255, 0.1);
		padding: 4rpx 12rpx;
		border-radius: 6rpx;
	}
	
	.coach-detail {
		display: flex;
		margin-bottom: 10rpx;
	}
	
	.detail-item {
		display: flex;
		align-items: center;
		margin-right: 30rpx;
	}
	
	.detail-text {
		font-size: 24rpx;
		color: #666666;
		margin-left: 6rpx;
	}
	
	.coach-tags {
		display: flex;
		flex-wrap: wrap;
	}
	
	.tag {
		font-size: 24rpx;
		color: #666666;
		background-color: #F5F5F5;
		padding: 4rpx 12rpx;
		border-radius: 6rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
	}
	
	.coach-actions {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}
	
	.action-btn {
		width: 60rpx;
		height: 60rpx;
		border-radius: 30rpx;
		background-color: rgba(0, 122, 255, 0.1);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 10rpx;
	}
	
	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 100rpx 0;
	}
	
	.empty-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
</style> 